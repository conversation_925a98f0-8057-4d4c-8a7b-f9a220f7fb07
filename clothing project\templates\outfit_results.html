{% extends "base.html" %}

{% block title %}Outfit Results - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-star me-3"></i>Your Perfect Outfits</h1>
    <p>AI-generated outfits for {{ occasion.value.title() }} in {{ weather.value.title() }} weather
    {% if preferred_style %} with {{ preferred_style.value.title() }} style{% endif %}</p>
</div>

<div id="outfitResults">
    {% if outfits %}
    <!-- Action Bar -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-magic me-2"></i>{{ outfits|length }} Outfits Generated
                    </h5>
                    <small class="text-muted">
                        {% if generation_mode == 'custom' and custom_items %}
                            Custom: {{ custom_items|join(', ')|title }}
                        {% else %}
                            Auto-generated complete outfits
                        {% endif %}
                    </small>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2 justify-content-md-end">
                        <button class="btn btn-outline-primary regenerate-btn">
                            <i class="fas fa-redo me-1"></i>Regenerate
                        </button>
                        <button class="btn btn-outline-secondary modify-search-btn">
                            <i class="fas fa-sliders-h me-1"></i>Modify Search
                        </button>
                        <button class="btn btn-outline-success save-all-btn">
                            <i class="fas fa-save me-1"></i>Save All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Outfit Results -->
    <div class="row mb-4">
        {% for outfit in outfits %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card outfit-card h-100">
                <div class="position-relative">
                    <!-- Outfit Score -->
                    <div class="outfit-score">
                        {{ "%.0f"|format(outfit.overall_score * 100) }}%
                    </div>
                    
                    <!-- Favorite Button -->
                    <button class="favorite-btn" onclick="toggleFavorite('{{ outfit.id }}', this)">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-tshirt me-2"></i>Outfit {{ loop.index }}
                    </h5>
                    
                    <!-- Outfit Items Display -->
                    <div class="outfit-items">
                        {% for item in outfit.items %}
                            {% if item.photo_filename %}
                            <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                 class="outfit-item-thumb" alt="{{ item.name }}" title="{{ item.name }}"
                                 data-bs-toggle="tooltip" data-bs-placement="top">
                            {% else %}
                            <div class="outfit-item-thumb-placeholder" title="{{ item.name }}"
                                 data-bs-toggle="tooltip" data-bs-placement="top">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Outfit Description -->
                    <p class="card-text">{{ outfit.description }}</p>

                    <!-- Color Palette -->
                    <div class="color-palette mb-3">
                        <div class="color-palette-label">
                            <i class="fas fa-palette me-1"></i>
                            <small>Color Palette</small>
                        </div>
                        <div class="color-circles">
                            {% set outfit_colors = [] %}
                            {% for item in outfit.items %}
                                {% if item.colors %}
                                    {% for color in item.colors %}
                                        {% if color.lower() not in outfit_colors %}
                                            {% set _ = outfit_colors.append(color.lower()) %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            {% endfor %}

                            {% set color_map = {
                                'black': '#000000', 'white': '#ffffff', 'gray': '#808080', 'grey': '#808080',
                                'red': '#dc3545', 'blue': '#007bff', 'green': '#28a745', 'yellow': '#ffc107',
                                'orange': '#fd7e14', 'purple': '#6f42c1', 'pink': '#e83e8c', 'brown': '#8b4513',
                                'beige': '#f5f5dc', 'navy': '#000080', 'maroon': '#800000', 'olive': '#808000',
                                'cream': '#fffdd0', 'tan': '#d2b48c', 'khaki': '#f0e68c'
                            } %}

                            {% for color in outfit_colors[:6] %}
                                {% set hex_color = color_map.get(color, '#6c757d') %}
                                <div class="color-circle" style="background-color: {{ hex_color }}"
                                     title="{{ color.title() }}"></div>
                            {% endfor %}

                            {% if outfit_colors|length == 0 %}
                                <div class="color-circle" style="background-color: #6c757d" title="Neutral"></div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Item List -->
                    <div class="mb-3">
                        <h6 class="text-muted">Items:</h6>
                        <ul class="list-unstyled">
                            {% for item in outfit.items %}
                            <li class="d-flex justify-content-between align-items-center mb-1">
                                <span>
                                    <i class="fas fa-{{ 'tshirt' if item.category == 'top' else 'shoe-prints' if item.category == 'shoes' else 'vest' if item.category == 'outerwear' else 'user-tie' if item.category == 'dress' else 'circle' }} me-2 text-muted"></i>
                                    {{ item.name }}
                                </span>
                                <a href="{{ url_for('view_item', item_id=item.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <!-- Score Breakdown with Explanations -->
                    <div class="score-breakdown mb-3">
                        <div class="score-breakdown-header mb-2">
                            <small class="text-muted">
                                <i class="fas fa-chart-line me-1"></i>
                                AI Analysis Breakdown
                            </small>
                        </div>

                        <div class="score-item mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="score-label">
                                    <i class="fas fa-palette me-1 text-primary"></i>
                                    <span>Style Harmony</span>
                                    <i class="fas fa-info-circle ms-1 text-muted"
                                       data-bs-toggle="tooltip"
                                       title="How well the clothing styles work together (casual, formal, etc.)"></i>
                                </div>
                                <div class="score-value">
                                    <span class="score-percentage">{{ "%.0f"|format(outfit.style_score * 100) }}%</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: {{ "%.0f"|format(outfit.style_score * 100) }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="score-item mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="score-label">
                                    <i class="fas fa-eye me-1 text-success"></i>
                                    <span>Color Harmony</span>
                                    <i class="fas fa-info-circle ms-1 text-muted"
                                       data-bs-toggle="tooltip"
                                       title="How well the colors complement each other and create visual balance"></i>
                                </div>
                                <div class="score-value">
                                    <span class="score-percentage">{{ "%.0f"|format(outfit.color_harmony * 100) }}%</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: {{ "%.0f"|format(outfit.color_harmony * 100) }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="score-item mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="score-label">
                                    <i class="fas fa-calendar-check me-1 text-warning"></i>
                                    <span>Occasion Fit</span>
                                    <i class="fas fa-info-circle ms-1 text-muted"
                                       data-bs-toggle="tooltip"
                                       title="How appropriate this outfit is for the selected occasion and weather"></i>
                                </div>
                                <div class="score-value">
                                    <span class="score-percentage">{{ "%.0f"|format(outfit.occasion_fit * 100) }}%</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: {{ "%.0f"|format(outfit.occasion_fit * 100) }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="score-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="score-label">
                                    <i class="fas fa-thermometer-half me-1 text-info"></i>
                                    <span>Weather Match</span>
                                    <i class="fas fa-info-circle ms-1 text-muted"
                                       data-bs-toggle="tooltip"
                                       title="How suitable this outfit is for the current weather conditions"></i>
                                </div>
                                <div class="score-value">
                                    <span class="score-percentage">85%</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 mb-2">
                        <button class="btn btn-success flex-fill save-outfit-btn"
                                data-index="{{ loop.index0 }}">
                            <i class="fas fa-save me-1"></i>Save Outfit
                        </button>
                        <button class="btn btn-outline-primary share-outfit-btn"
                                data-outfit-id="{{ outfit.id }}"
                                data-bs-toggle="tooltip" title="Share Outfit">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="d-flex gap-1">
                        <button class="btn btn-outline-secondary btn-sm flex-fill rate-outfit-btn"
                                data-index="{{ loop.index0 }}">
                            <i class="fas fa-star me-1"></i>Rate
                        </button>
                        <button class="btn btn-outline-info btn-sm flex-fill modify-outfit-btn">
                            <i class="fas fa-edit me-1"></i>Modify
                        </button>
                        <button class="btn btn-outline-warning btn-sm flex-fill try-on-outfit-btn">
                            <i class="fas fa-user me-1"></i>Try On
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Want More Options?</h5>
                    <div class="d-flex gap-2 justify-content-center flex-wrap">
                        <button class="btn btn-primary" onclick="regenerateOutfits()">
                            <i class="fas fa-redo me-2"></i>Generate More Outfits
                        </button>
                        <a href="{{ url_for('generate_outfit') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sliders-h me-2"></i>Change Preferences
                        </a>
                        <a href="{{ url_for('add_item') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-plus me-2"></i>Add More Items
                        </a>
                        <a href="{{ url_for('outfit_history') }}" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>View History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- No Outfits Found -->
    <div class="card text-center">
        <div class="card-body py-5">
            <i class="fas fa-search fa-4x text-muted mb-4"></i>
            <h4>No Suitable Outfits Found</h4>
            <p class="text-muted mb-4">
                We couldn't find any outfits that match your criteria. Here are some suggestions:
            </p>
            
            <div class="row text-start">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-plus-circle text-primary me-2"></i>Add More Items</h6>
                            <p class="small text-muted mb-2">
                                Your closet might need more variety for this occasion and weather.
                            </p>
                            <a href="{{ url_for('add_item') }}" class="btn btn-sm btn-primary">Add Items</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-sliders-h text-info me-2"></i>Adjust Preferences</h6>
                            <p class="small text-muted mb-2">
                                Try different occasion, weather, or style preferences.
                            </p>
                            <a href="{{ url_for('generate_outfit') }}" class="btn btn-sm btn-info">Change Settings</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>Current Search Criteria:</h6>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <span class="badge bg-primary">{{ occasion.value.title() }}</span>
                    <span class="badge bg-info">{{ weather.value.title() }}</span>
                    {% if preferred_style %}
                    <span class="badge bg-secondary">{{ preferred_style.value.title() }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share-alt me-2"></i>Share Outfit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Share this outfit with friends:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="copyOutfitLink()">
                        <i class="fas fa-copy me-2"></i>Copy Link
                    </button>
                    <button class="btn btn-outline-success" onclick="shareToWhatsApp()">
                        <i class="fab fa-whatsapp me-2"></i>Share to WhatsApp
                    </button>
                    <button class="btn btn-outline-info" onclick="shareToTwitter()">
                        <i class="fab fa-twitter me-2"></i>Share to Twitter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Save Outfit Modal -->
<div class="modal fade" id="saveOutfitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-save me-2"></i>Save Outfit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="saveOutfitForm">
                    <div class="mb-3">
                        <label for="outfitName" class="form-label">Outfit Name</label>
                        <input type="text" class="form-control" id="outfitName"
                               placeholder="e.g., Work Meeting Look, Date Night Outfit">
                        <div class="form-text">Give your outfit a memorable name</div>
                    </div>

                    <div class="mb-3">
                        <label for="outfitNotes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="outfitNotes" rows="3"
                                  placeholder="Add any notes about this outfit..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="outfitRating" class="form-label">Rate this outfit</label>
                        <div class="rating-stars" id="outfitRating">
                            <i class="far fa-star" data-rating="1"></i>
                            <i class="far fa-star" data-rating="2"></i>
                            <i class="far fa-star" data-rating="3"></i>
                            <i class="far fa-star" data-rating="4"></i>
                            <i class="far fa-star" data-rating="5"></i>
                        </div>
                        <div class="form-text">Rate from 1 to 5 stars</div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="markAsFavorite">
                        <label class="form-check-label" for="markAsFavorite">
                            <i class="fas fa-heart text-danger me-1"></i>Mark as favorite
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="saveOutfitWithDetails()">
                    <i class="fas fa-save me-1"></i>Save Outfit
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rate Outfit Modal -->
<div class="modal fade" id="rateOutfitModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-star me-2"></i>Rate Outfit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <p>How do you like this outfit?</p>
                <div class="rating-stars-large" id="quickRating">
                    <i class="far fa-star" data-rating="1"></i>
                    <i class="far fa-star" data-rating="2"></i>
                    <i class="far fa-star" data-rating="3"></i>
                    <i class="far fa-star" data-rating="4"></i>
                    <i class="far fa-star" data-rating="5"></i>
                </div>
                <div class="mt-3">
                    <small class="text-muted">Click to rate</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Try On Modal -->
<div class="modal fade" id="tryOnModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>Try On Outfit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Virtual Try-On</strong><br>
                    Take a photo of yourself wearing this outfit to see how it looks!
                </div>

                <div class="text-center">
                    <button class="btn btn-primary btn-lg" onclick="openCamera()">
                        <i class="fas fa-camera me-2"></i>Take Photo
                    </button>
                    <p class="mt-2 text-muted">Or upload an existing photo</p>
                    <input type="file" class="form-control" accept="image/*" id="tryOnPhoto">
                </div>

                <div id="tryOnPreview" class="mt-3" style="display: none;">
                    <img id="tryOnImage" class="img-fluid rounded" style="max-height: 300px;">
                    <div class="mt-2">
                        <button class="btn btn-success" onclick="saveTryOnPhoto()">
                            <i class="fas fa-save me-1"></i>Save Try-On Photo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.outfit-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.outfit-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.outfit-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    z-index: 10;
}

/* Enhanced Score Breakdown */
.score-breakdown {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.score-breakdown-header {
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.score-item {
    background: white;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.score-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
}

.score-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 80px;
}

.score-percentage {
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
    min-width: 35px;
    text-align: right;
}

.score-bar {
    width: 40px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #ffc107 50%, #dc3545 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.outfit-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 15px 0;
}

.outfit-item-thumb {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.outfit-item-thumb:hover {
    transform: scale(1.1);
}

.outfit-item-thumb-placeholder {
    width: 70px;
    height: 70px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #6c757d;
}

.favorite-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e17055;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 10;
}

.favorite-btn:hover {
    background: white;
    transform: scale(1.1);
}

.favorite-btn.active {
    color: #e74c3c;
}

/* Rating Stars */
.rating-stars {
    font-size: 1.2rem;
    cursor: pointer;
}

.rating-stars i {
    color: #ddd;
    transition: color 0.2s ease;
    margin-right: 0.2rem;
}

.rating-stars i:hover,
.rating-stars i.active {
    color: #ffc107;
}

.rating-stars-large {
    font-size: 2rem;
    cursor: pointer;
}

.rating-stars-large i {
    color: #ddd;
    transition: all 0.2s ease;
    margin: 0 0.3rem;
}

.rating-stars-large i:hover,
.rating-stars-large i.active {
    color: #ffc107;
    transform: scale(1.1);
}

/* Enhanced Actions */
.outfit-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.outfit-card:hover .outfit-actions {
    opacity: 1;
}

.action-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

.btn-group-custom {
    display: flex;
    gap: 0.5rem;
}

.btn-group-custom .btn {
    flex: 1;
}

/* Try-On Preview */
#tryOnPreview {
    text-align: center;
}

/* Color Palette Styles */
.color-palette {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.color-palette-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
    font-weight: 500;
}

.color-circles {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.color-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
}

.color-circle:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.color-circle[style*="ffffff"] {
    border-color: #dee2e6;
}

.color-circle[style*="000000"] {
    border-color: #6c757d;
}

@media (max-width: 768px) {
    .outfit-items {
        justify-content: flex-start;
    }

    .outfit-item-thumb, .outfit-item-thumb-placeholder {
        width: 50px;
        height: 50px;
    }

    .rating-stars-large {
        font-size: 1.5rem;
    }

    .rating-stars-large i {
        margin: 0 0.2rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Store outfit data globally
window.outfitData = [
    {% for outfit in outfits %}
    {{ outfit|safe_json|safe }}{% if not loop.last %},{% endif %}
    {% endfor %}
];

let currentOutfitData = null;
let currentOutfitIndex = null;

// Enhanced Save Outfit Modal
function openSaveModal(outfitData, index) {
    currentOutfitData = outfitData;
    currentOutfitIndex = index;

    // Generate default name
    const occasion = outfitData.occasion.replace('_', ' ');
    const weather = outfitData.weather.replace('_', ' ');
    const defaultName = `${occasion.charAt(0).toUpperCase() + occasion.slice(1)} outfit for ${weather} weather`;

    document.getElementById('outfitName').value = defaultName;
    document.getElementById('outfitNotes').value = '';
    document.getElementById('markAsFavorite').checked = false;

    // Reset rating stars
    document.querySelectorAll('#outfitRating i').forEach(star => {
        star.className = 'far fa-star';
    });

    const modal = new bootstrap.Modal(document.getElementById('saveOutfitModal'));
    modal.show();
}

function saveOutfitWithDetails() {
    if (!currentOutfitData) return;

    const name = document.getElementById('outfitName').value.trim();
    const notes = document.getElementById('outfitNotes').value.trim();
    const isFavorite = document.getElementById('markAsFavorite').checked;
    const rating = document.querySelectorAll('#outfitRating i.active').length;

    if (!name) {
        showAlert('Please enter an outfit name', 'error');
        return;
    }

    // Enhance outfit data with user input
    const enhancedOutfitData = {
        ...currentOutfitData,
        description: name,
        notes: notes,
        is_favorite: isFavorite,
        rating: rating > 0 ? rating : null
    };

    const saveButton = document.querySelector('#saveOutfitModal .btn-success');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(enhancedOutfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the outfit card to show it's saved
            const outfitCard = document.querySelectorAll('.outfit-card')[currentOutfitIndex];
            const saveBtn = outfitCard.querySelector('.btn-success');
            saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
            saveBtn.disabled = true;
            saveBtn.classList.add('btn-outline-success');
            saveBtn.classList.remove('btn-success');

            bootstrap.Modal.getInstance(document.getElementById('saveOutfitModal')).hide();
            showAlert('Outfit saved successfully!', 'success');
        } else {
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving outfit', 'error');
    })
    .finally(() => {
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
    });
}

// Rating functionality
function rateOutfit(index) {
    currentOutfitIndex = index;

    // Reset rating stars
    document.querySelectorAll('#quickRating i').forEach(star => {
        star.className = 'far fa-star';
    });

    const modal = new bootstrap.Modal(document.getElementById('rateOutfitModal'));
    modal.show();
}

// Modify outfit functionality
function modifyOutfit(outfitData) {
    // Store outfit data and redirect to generator with pre-filled data
    sessionStorage.setItem('modifyOutfitData', JSON.stringify(outfitData));
    window.location.href = '/generate-outfit?modify=true';
}

// Try on functionality
function tryOnOutfit(outfitData) {
    currentOutfitData = outfitData;
    const modal = new bootstrap.Modal(document.getElementById('tryOnModal'));
    modal.show();
}

// Action bar functions
function regenerateOutfits() {
    // Show loading and regenerate
    document.getElementById('outfitResults').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
            <h5>Regenerating Outfits...</h5>
            <p class="text-muted">Creating new combinations for you</p>
        </div>
    `;

    // Resubmit the form
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/generate-outfit';

    // Add form data from current search
    const formData = new FormData();
    formData.append('occasion', '{{ occasion.value }}');
    formData.append('weather', '{{ weather.value }}');
    {% if preferred_style %}
    formData.append('style', '{{ preferred_style.value }}');
    {% endif %}
    {% if generation_mode == 'custom' and custom_items %}
    formData.append('generation_mode', 'custom');
    {% for item in custom_items %}
    formData.append('custom_items', '{{ item }}');
    {% endfor %}
    {% endif %}

    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
}

function modifySearch() {
    window.location.href = '/generate-outfit';
}

function saveAllOutfits(event) {
    const outfits = window.outfitData;
    let savedCount = 0;
    let totalOutfits = outfits.length;

    const saveButton = event ? event.target : document.querySelector('.save-all-btn');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving All...';

    outfits.forEach((outfit, index) => {
        const enhancedOutfit = {
            ...outfit,
            description: `Auto-saved outfit ${index + 1}`,
            notes: 'Bulk saved from outfit generation'
        };

        fetch('/save-outfit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(enhancedOutfit)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                savedCount++;
                if (savedCount === totalOutfits) {
                    saveButton.innerHTML = '<i class="fas fa-check me-1"></i>All Saved!';
                    saveButton.classList.add('btn-outline-success');
                    saveButton.classList.remove('btn-outline-success');
                    showAlert(`All ${totalOutfits} outfits saved successfully!`, 'success');
                }
            }
        })
        .catch(error => {
            console.error('Error saving outfit:', error);
        });
    });
}

function shareOutfit(outfitId) {
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
    
    // Store outfit ID for sharing functions
    window.currentOutfitId = outfitId;
}

function copyOutfitLink() {
    const url = `${window.location.origin}/outfit/${window.currentOutfitId}`;
    navigator.clipboard.writeText(url).then(() => {
        showAlert('Outfit link copied to clipboard!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('shareModal')).hide();
    }).catch(() => {
        showAlert('Failed to copy link', 'error');
    });
}

function shareToWhatsApp() {
    const text = `Check out this amazing outfit I created with my Personal Closet app!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

// Button event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Save outfit buttons
    document.querySelectorAll('.save-outfit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            const outfitData = window.outfitData[index];
            openSaveModal(outfitData, index);
        });
    });

    // Share outfit buttons
    document.querySelectorAll('.share-outfit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const outfitId = this.dataset.outfitId;
            shareOutfit(outfitId);
        });
    });

    // Rate outfit buttons
    document.querySelectorAll('.rate-outfit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            rateOutfit(index);
        });
    });

    // Modify outfit buttons
    document.querySelectorAll('.modify-outfit-btn').forEach((button, index) => {
        button.addEventListener('click', function() {
            const outfitData = window.outfitData[index];
            modifyOutfit(outfitData);
        });
    });

    // Try on outfit buttons
    document.querySelectorAll('.try-on-outfit-btn').forEach((button, index) => {
        button.addEventListener('click', function() {
            const outfitData = window.outfitData[index];
            tryOnOutfit(outfitData);
        });
    });

    // Action bar buttons
    const regenerateBtn = document.querySelector('.regenerate-btn');
    if (regenerateBtn) {
        regenerateBtn.addEventListener('click', regenerateOutfits);
    }

    const modifySearchBtn = document.querySelector('.modify-search-btn');
    if (modifySearchBtn) {
        modifySearchBtn.addEventListener('click', modifySearch);
    }

    const saveAllBtn = document.querySelector('.save-all-btn');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllOutfits);
    }

// Star rating functionality
    // Handle rating stars in save modal
    document.querySelectorAll('#outfitRating i').forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            const stars = document.querySelectorAll('#outfitRating i');

            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star active';
                } else {
                    s.className = 'far fa-star';
                }
            });
        });

        star.addEventListener('mouseover', function() {
            const rating = parseInt(this.dataset.rating);
            const stars = document.querySelectorAll('#outfitRating i');

            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star';
                } else {
                    s.className = 'far fa-star';
                }
            });
        });
    });

    // Handle quick rating stars
    document.querySelectorAll('#quickRating i').forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);

            // Update visual feedback
            const stars = document.querySelectorAll('#quickRating i');
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star active';
                } else {
                    s.className = 'far fa-star';
                }
            });

            // Auto-close modal after rating
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('rateOutfitModal')).hide();
                showAlert(`Rated ${rating} star${rating > 1 ? 's' : ''}!`, 'success');
            }, 500);
        });

        star.addEventListener('mouseover', function() {
            const rating = parseInt(this.dataset.rating);
            const stars = document.querySelectorAll('#quickRating i');

            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star';
                } else {
                    s.className = 'far fa-star';
                }
            });
        });
    });

    // Reset stars on mouse leave
    document.getElementById('quickRating').addEventListener('mouseleave', function() {
        document.querySelectorAll('#quickRating i').forEach(star => {
            if (!star.classList.contains('active')) {
                star.className = 'far fa-star';
            }
        });
    });
});

// Camera and try-on functionality
function openCamera() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                // Create video element for camera preview
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();

                // Replace the camera button with video preview
                const tryOnModal = document.getElementById('tryOnModal');
                const modalBody = tryOnModal.querySelector('.modal-body');
                modalBody.innerHTML = `
                    <div class="text-center">
                        <video id="cameraPreview" width="300" height="200" autoplay></video>
                        <br>
                        <button class="btn btn-success mt-2" onclick="capturePhoto()">
                            <i class="fas fa-camera me-1"></i>Capture Photo
                        </button>
                        <button class="btn btn-secondary mt-2" onclick="stopCamera()">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                    </div>
                `;

                document.getElementById('cameraPreview').srcObject = stream;
                window.currentStream = stream;
            })
            .catch(function(error) {
                console.error('Error accessing camera:', error);
                showAlert('Unable to access camera. Please check permissions.', 'error');
            });
    } else {
        showAlert('Camera not supported in this browser', 'error');
    }
}

function capturePhoto() {
    const video = document.getElementById('cameraPreview');
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);

    // Convert to blob and display
    canvas.toBlob(function(blob) {
        const url = URL.createObjectURL(blob);
        document.getElementById('tryOnImage').src = url;
        document.getElementById('tryOnPreview').style.display = 'block';

        // Stop camera
        stopCamera();

        // Update modal content
        const modalBody = document.getElementById('tryOnModal').querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="text-center">
                <img id="tryOnImage" src="${url}" class="img-fluid rounded" style="max-height: 300px;">
                <div class="mt-3">
                    <button class="btn btn-success" onclick="saveTryOnPhoto()">
                        <i class="fas fa-save me-1"></i>Save Try-On Photo
                    </button>
                    <button class="btn btn-secondary" onclick="retakePhoto()">
                        <i class="fas fa-redo me-1"></i>Retake
                    </button>
                </div>
            </div>
        `;

        window.currentTryOnBlob = blob;
    });
}

function stopCamera() {
    if (window.currentStream) {
        window.currentStream.getTracks().forEach(track => track.stop());
        window.currentStream = null;
    }
}

function retakePhoto() {
    openCamera();
}

function saveTryOnPhoto() {
    if (window.currentTryOnBlob && currentOutfitData) {
        const formData = new FormData();
        formData.append('photo', window.currentTryOnBlob, 'try-on.jpg');
        formData.append('outfit_id', currentOutfitData.id);

        fetch('/save-try-on-photo', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Try-on photo saved!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('tryOnModal')).hide();
            } else {
                showAlert('Error saving photo: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error saving photo', 'error');
        });
    }
}

// Handle file upload for try-on
document.getElementById('tryOnPhoto').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('tryOnImage').src = e.target.result;
            document.getElementById('tryOnPreview').style.display = 'block';
            window.currentTryOnBlob = file;
        };
        reader.readAsDataURL(file);
    }
});

function shareToTwitter() {
    const text = `Just created an amazing outfit with my Personal Closet app! 👗✨ #PersonalStyle #OOTD`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
