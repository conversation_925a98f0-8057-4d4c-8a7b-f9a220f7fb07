#!/usr/bin/env python3
"""
Privacy Monitor for VPN Server
Monitors and enhances privacy features
"""

import time
import subprocess
import requests
import json
import logging
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.logger import get_logger

logger = get_logger(__name__)

class PrivacyMonitor:
    """Monitor and enhance VPN privacy features"""
    
    def __init__(self):
        self.running = True
        self.blocked_domains = set()
        self.load_blocklists()
        
    def load_blocklists(self):
        """Load and update ad/tracker blocklists"""
        try:
            logger.info("Loading privacy blocklists...")
            
            # Common blocklist sources
            blocklist_urls = [
                "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts",
                "https://someonewhocares.org/hosts/zero/hosts",
                "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"
            ]
            
            for url in blocklist_urls:
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        self.parse_blocklist(response.text)
                        logger.info(f"Loaded blocklist from {url}")
                except Exception as e:
                    logger.warning(f"Failed to load blocklist from {url}: {e}")
            
            logger.info(f"Total blocked domains: {len(self.blocked_domains)}")
            
        except Exception as e:
            logger.error(f"Failed to load blocklists: {e}")
    
    def parse_blocklist(self, content):
        """Parse blocklist content and extract domains"""
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # Parse different formats
                if line.startswith('0.0.0.0 '):
                    domain = line.split(' ', 1)[1].strip()
                    if domain and '.' in domain:
                        self.blocked_domains.add(domain)
                elif line.startswith('127.0.0.1 '):
                    domain = line.split(' ', 1)[1].strip()
                    if domain and '.' in domain:
                        self.blocked_domains.add(domain)
                elif '||' in line and '^' in line:
                    # AdBlock format
                    domain = line.replace('||', '').replace('^', '').strip()
                    if domain and '.' in domain:
                        self.blocked_domains.add(domain)
    
    def update_dns_blocking(self):
        """Update DNS blocking rules"""
        try:
            logger.info("Updating DNS blocking rules...")
            
            # Update Unbound configuration with new blocked domains
            unbound_config = "/etc/unbound/unbound.conf"
            
            # Add new blocked domains to Unbound
            with open(unbound_config, 'a') as f:
                for domain in list(self.blocked_domains)[:1000]:  # Limit to prevent config bloat
                    f.write(f'    local-zone: "{domain}" refuse\n')
            
            # Reload Unbound
            subprocess.run(['unbound-control', 'reload'], check=True)
            logger.info("DNS blocking rules updated")
            
        except Exception as e:
            logger.error(f"Failed to update DNS blocking: {e}")
    
    def monitor_connections(self):
        """Monitor VPN connections for privacy leaks"""
        try:
            # Check for DNS leaks
            result = subprocess.run(['netstat', '-tuln'], capture_output=True, text=True)
            if result.returncode == 0:
                # Look for unexpected DNS connections
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':53 ' in line and '127.0.0.1' not in line:
                        logger.warning(f"Potential DNS leak detected: {line}")
            
            # Check for WebRTC leaks (simplified)
            self.check_webrtc_leaks()
            
        except Exception as e:
            logger.error(f"Connection monitoring failed: {e}")
    
    def check_webrtc_leaks(self):
        """Check for WebRTC IP leaks"""
        try:
            # This is a simplified check - in practice, you'd need more sophisticated detection
            logger.debug("Checking for WebRTC leaks...")
            
            # Check if WebRTC blocking is active
            iptables_result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
            if 'webrtc' not in iptables_result.stdout.lower():
                logger.info("Consider adding WebRTC blocking rules")
                
        except Exception as e:
            logger.debug(f"WebRTC leak check failed: {e}")
    
    def rotate_exit_ip(self):
        """Rotate exit IP if multiple IPs are available"""
        try:
            # This would be implemented if you have multiple exit IPs
            logger.debug("IP rotation not implemented - single exit IP")
            
        except Exception as e:
            logger.error(f"IP rotation failed: {e}")
    
    def check_privacy_status(self):
        """Check overall privacy status"""
        try:
            logger.info("Privacy status check:")
            
            # Check if VPN is running
            vpn_status = subprocess.run(['pgrep', '-f', 'vpn-server'], capture_output=True)
            if vpn_status.returncode == 0:
                logger.info("✅ VPN server is running")
            else:
                logger.warning("❌ VPN server not detected")
            
            # Check if DNS resolver is running
            dns_status = subprocess.run(['pgrep', '-f', 'unbound'], capture_output=True)
            if dns_status.returncode == 0:
                logger.info("✅ Privacy DNS resolver is running")
            else:
                logger.warning("❌ DNS resolver not detected")
            
            # Check firewall rules
            fw_status = subprocess.run(['iptables', '-L', '-n'], capture_output=True, text=True)
            if 'DROP' in fw_status.stdout:
                logger.info("✅ Firewall rules are active")
            else:
                logger.warning("❌ Firewall rules may not be properly configured")
                
        except Exception as e:
            logger.error(f"Privacy status check failed: {e}")
    
    def run(self):
        """Main monitoring loop"""
        logger.info("🛡️  Privacy Monitor started")
        
        # Initial setup
        self.check_privacy_status()
        
        # Update blocking rules
        self.update_dns_blocking()
        
        # Main monitoring loop
        while self.running:
            try:
                # Monitor connections every 30 seconds
                self.monitor_connections()
                
                # Update blocklists every hour
                if int(time.time()) % 3600 == 0:
                    self.load_blocklists()
                    self.update_dns_blocking()
                
                # Privacy status check every 5 minutes
                if int(time.time()) % 300 == 0:
                    self.check_privacy_status()
                
                time.sleep(30)
                
            except KeyboardInterrupt:
                logger.info("Privacy Monitor stopping...")
                self.running = False
            except Exception as e:
                logger.error(f"Monitor error: {e}")
                time.sleep(60)  # Wait before retrying

def main():
    """Main entry point"""
    logging.basicConfig(level=logging.INFO)
    
    monitor = PrivacyMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
