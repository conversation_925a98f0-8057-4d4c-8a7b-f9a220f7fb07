{"version": 3, "file": "deepAssign.js", "sourceRoot": "", "sources": ["../src/deepAssign.ts"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,CAAC,CAAM;IACtB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,CAAA;IACrB,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAA;AACjD,CAAC;AAED,SAAS,SAAS,CAAC,MAAW,EAAE,IAAS,EAAE,GAAW;IACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;IACvB,iEAAiE;IACjE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,OAAM;IACR,CAAC;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;IAC7B,IAAI,SAAS,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACnF,gBAAgB;QAChB,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC5D,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QACrB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;IACxC,CAAC;AACH,CAAC;AAED,SAAS,MAAM,CAAC,EAAO,EAAE,IAAS;IAChC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QAChB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;QAC1B,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAA;AACX,CAAC;AAED,SAAgB,UAAU,CAAI,MAAS,EAAE,GAAG,OAAmB;IAC7D,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACd,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;QACnB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAPD,gCAOC", "sourcesContent": ["function isObject(x: any) {\n  if (Array.isArray(x)) {\n    return false\n  }\n\n  const type = typeof x\n  return type === \"object\" || type === \"function\"\n}\n\nfunction assignKey(target: any, from: any, key: string) {\n  const value = from[key]\n  // https://github.com/electron-userland/electron-builder/pull/562\n  if (value === undefined) {\n    return\n  }\n\n  const prevValue = target[key]\n  if (prevValue == null || value == null || !isObject(prevValue) || !isObject(value)) {\n    // Merge arrays.\n    if (Array.isArray(prevValue) && Array.isArray(value)) {\n      target[key] = Array.from(new Set(prevValue.concat(value)))\n    } else {\n      target[key] = value\n    }\n  } else {\n    target[key] = assign(prevValue, value)\n  }\n}\n\nfunction assign(to: any, from: any) {\n  if (to !== from) {\n    for (const key of Object.getOwnPropertyNames(from)) {\n      assignKey(to, from, key)\n    }\n  }\n  return to\n}\n\nexport function deepAssign<T>(target: T, ...objects: Array<any>): T {\n  for (const o of objects) {\n    if (o != null) {\n      assign(target, o)\n    }\n  }\n  return target\n}\n"]}