const { app, BrowserWindow, Menu, shell, dialog } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');

let mainWindow;
let dockerProcess;

// Keep a global reference of the window object
function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    show: false, // Don't show until ready
    titleBarStyle: 'default'
  });

  // Set window title
  mainWindow.setTitle('Personal Closet - AI Wardrobe Manager');

  // Start Docker container and load the app
  startDockerAndLoadApp();

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    stopDocker();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function startDockerAndLoadApp() {
  // Show loading page first
  mainWindow.loadFile(path.join(__dirname, 'loading.html'));

  // Check if Docker is already running first
  console.log('Checking Docker status...');
  const checkDocker = spawn('docker-compose', ['ps'], {
    cwd: __dirname,
    stdio: 'pipe'
  });

  let dockerOutput = '';
  checkDocker.stdout.on('data', (data) => {
    dockerOutput += data.toString();
  });

  checkDocker.on('close', (code) => {
    if (code === 0 && dockerOutput.includes('Up')) {
      console.log('Docker container is already running');
      // Container is already running, just load the app
      setTimeout(() => {
        loadApp();
      }, 2000);
    } else {
      // Start Docker container
      console.log('Starting Docker container...');
      dockerProcess = spawn('docker-compose', ['up', '-d'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      dockerProcess.on('close', (code) => {
        if (code === 0) {
          console.log('Docker container started successfully');
          // Wait a moment for the server to be ready, then load the app
          setTimeout(() => {
            loadApp();
          }, 3000);
        } else {
          console.error('Failed to start Docker container');
          showErrorDialog('Failed to start the application. Please make sure Docker is installed and running.');
        }
      });

      dockerProcess.on('error', (error) => {
        console.error('Docker process error:', error);
        showErrorDialog('Failed to start Docker. Please make sure Docker is installed and running.');
      });
    }
  });

  checkDocker.on('error', (error) => {
    console.error('Docker check error:', error);
    showErrorDialog('Failed to check Docker status. Please make sure Docker is installed and running.');
  });
}

function loadApp() {
  // Try to load the app, with retry logic
  // First try development version on port 5001, then fallback to production on port 5000
  const tryLoad = (attempts = 0) => {
    const devUrl = 'http://localhost:5001';
    const prodUrl = 'http://localhost:5000';

    // Try development version first
    mainWindow.loadURL(devUrl)
      .then(() => {
        console.log('Development app loaded successfully on port 5001');
      })
      .catch((devError) => {
        console.log(`Development app load failed, trying production:`, devError.message);
        // Try production version as fallback
        mainWindow.loadURL(prodUrl)
          .then(() => {
            console.log('Production app loaded successfully on port 5000');
          })
          .catch((prodError) => {
            console.log(`Load attempt ${attempts + 1} failed for both dev and prod:`, prodError.message);
            if (attempts < 10) {
              // Retry after 2 seconds
              setTimeout(() => tryLoad(attempts + 1), 2000);
            } else {
              showErrorDialog('Failed to connect to the application after multiple attempts.');
            }
          });
      });
  };

  tryLoad();
}

function stopDocker() {
  if (dockerProcess) {
    console.log('Stopping Docker container...');
    spawn('docker-compose', ['down'], {
      cwd: __dirname,
      stdio: 'inherit'
    });
  }
}

function showErrorDialog(message) {
  dialog.showErrorBox('Personal Closet Error', message);
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Refresh',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            if (mainWindow) {
              mainWindow.reload();
            }
          }
        },
        {
          label: 'Toggle Developer Tools',
          accelerator: 'F12',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.toggleDevTools();
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            if (mainWindow) {
              const currentZoom = mainWindow.webContents.getZoomLevel();
              mainWindow.webContents.setZoomLevel(currentZoom + 1);
            }
          }
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            if (mainWindow) {
              const currentZoom = mainWindow.webContents.getZoomLevel();
              mainWindow.webContents.setZoomLevel(currentZoom - 1);
            }
          }
        },
        {
          label: 'Reset Zoom',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.setZoomLevel(0);
            }
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Personal Closet',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Personal Closet',
              message: 'Personal Closet v1.0.0',
              detail: 'AI-Powered Wardrobe Management Application\n\nManage your wardrobe, generate outfits, and discover new fashion with AI assistance.'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  stopDocker();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  stopDocker();
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});
