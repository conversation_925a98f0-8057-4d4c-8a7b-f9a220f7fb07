# Privacy-focused VPN Server Docker Image
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    iproute2 \
    iptables \
    dnsutils \
    curl \
    wget \
    unbound \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Create VPN user
RUN useradd -r -s /bin/false vpnuser

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy VPN application
COPY src/ ./src/
COPY cli/ ./cli/
COPY config/ ./config/
COPY setup.py .

# Install VPN package
RUN pip install -e .

# Create necessary directories
RUN mkdir -p /var/log/vpn /var/run/vpn /etc/vpn/certs

# Copy configuration files
COPY docker/unbound.conf /etc/unbound/unbound.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/entrypoint.sh /entrypoint.sh
COPY docker/privacy-config.yaml /etc/vpn/privacy-config.yaml

# Make entrypoint executable
RUN chmod +x /entrypoint.sh

# Expose VPN port
EXPOSE 1194/udp
EXPOSE 1194/tcp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python cli/server_cli.py status || exit 1

# Set capabilities for network operations
# Note: In production, run with --cap-add=NET_ADMIN --device /dev/net/tun
ENTRYPOINT ["/entrypoint.sh"]
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
