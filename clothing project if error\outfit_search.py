#!/usr/bin/env python3
"""
Outfit Search Module - Internet search for outfits with pricing
Searches various fashion websites for outfit recommendations with prices
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import quote_plus, urljoin
import time
import random

@dataclass
class OutfitSearchResult:
    """Represents a search result for an outfit item"""
    title: str
    price: str
    original_price: Optional[str]
    discount: Optional[str]
    image_url: str
    product_url: str
    store: str
    rating: Optional[float]
    reviews_count: Optional[int]
    description: Optional[str]
    category: str

class OutfitSearchEngine:
    """Search engine for finding outfits online with pricing"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Search engines and fashion sites
        self.search_sources = {
            'google_shopping': {
                'base_url': 'https://www.google.com/search',
                'params': {'tbm': 'shop', 'q': '{}'},
                'enabled': True
            },
            'amazon_fashion': {
                'base_url': 'https://www.amazon.com/s',
                'params': {'k': '{}', 'rh': 'n:7141123011'},
                'enabled': True
            }
        }
    
    def search_outfits(self, 
                      style_query: str, 
                      occasion: str = "casual",
                      gender: str = "unisex",
                      max_results: int = 20,
                      price_range: Optional[tuple] = None) -> List[OutfitSearchResult]:
        """
        Search for outfits based on style query
        
        Args:
            style_query: The style description (e.g., "casual summer outfit")
            occasion: The occasion (casual, formal, business, etc.)
            gender: Target gender (men, women, unisex)
            max_results: Maximum number of results to return
            price_range: Optional tuple of (min_price, max_price)
        
        Returns:
            List of OutfitSearchResult objects
        """
        
        # Build comprehensive search query
        search_query = self._build_search_query(style_query, occasion, gender)
        
        all_results = []
        
        # Search Google Shopping
        if self.search_sources['google_shopping']['enabled']:
            try:
                google_results = self._search_google_shopping(search_query, max_results // 2)
                all_results.extend(google_results)
                time.sleep(random.uniform(1, 2))  # Rate limiting
            except Exception as e:
                print(f"Google Shopping search failed: {e}")
        
        # Search Amazon Fashion (simplified)
        if self.search_sources['amazon_fashion']['enabled']:
            try:
                amazon_results = self._search_amazon_fashion(search_query, max_results // 2)
                all_results.extend(amazon_results)
                time.sleep(random.uniform(1, 2))  # Rate limiting
            except Exception as e:
                print(f"Amazon Fashion search failed: {e}")
        
        # Filter by price range if specified
        if price_range:
            all_results = self._filter_by_price_range(all_results, price_range)
        
        # Sort by relevance and price
        all_results = self._sort_results(all_results)
        
        return all_results[:max_results]
    
    def _build_search_query(self, style_query: str, occasion: str, gender: str) -> str:
        """Build a comprehensive search query"""
        query_parts = []
        
        # Add the main style query
        query_parts.append(style_query)
        
        # Add occasion if not already in style_query
        if occasion.lower() not in style_query.lower():
            query_parts.append(occasion)
        
        # Add gender specification
        if gender != "unisex":
            query_parts.append(f"{gender}s")
        
        # Add clothing-specific terms
        clothing_terms = ["outfit", "clothing", "fashion"]
        if not any(term in style_query.lower() for term in clothing_terms):
            query_parts.append("outfit")
        
        return " ".join(query_parts)
    
    def _search_google_shopping(self, query: str, max_results: int) -> List[OutfitSearchResult]:
        """Search Google Shopping for outfit items"""
        results = []
        
        try:
            # Build search URL
            search_url = self.search_sources['google_shopping']['base_url']
            params = self.search_sources['google_shopping']['params'].copy()
            params['q'] = query
            
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Parse Google Shopping results
            product_divs = soup.find_all('div', {'data-docid': True})
            
            for div in product_divs[:max_results]:
                try:
                    result = self._parse_google_shopping_item(div)
                    if result:
                        results.append(result)
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"Google Shopping search error: {e}")
        
        return results
    
    def _parse_google_shopping_item(self, item_div) -> Optional[OutfitSearchResult]:
        """Parse a single Google Shopping item"""
        try:
            # Extract title
            title_elem = item_div.find('h3') or item_div.find('a')
            title = title_elem.get_text(strip=True) if title_elem else "Unknown Item"
            
            # Extract price
            price_elem = item_div.find('span', string=re.compile(r'\$\d+'))
            price = price_elem.get_text(strip=True) if price_elem else "Price not available"
            
            # Extract image
            img_elem = item_div.find('img')
            image_url = img_elem.get('src', '') if img_elem else ''
            
            # Extract product URL
            link_elem = item_div.find('a')
            product_url = link_elem.get('href', '') if link_elem else ''
            
            return OutfitSearchResult(
                title=title,
                price=price,
                original_price=None,
                discount=None,
                image_url=image_url,
                product_url=product_url,
                store="Google Shopping",
                rating=None,
                reviews_count=None,
                description=None,
                category="clothing"
            )
            
        except Exception as e:
            return None
    
    def _search_amazon_fashion(self, query: str, max_results: int) -> List[OutfitSearchResult]:
        """Search Amazon Fashion (simplified implementation)"""
        results = []
        
        # For demo purposes, return some mock results
        # In a real implementation, you would scrape Amazon or use their API
        mock_items = [
            {
                'title': f'{query} - Premium Quality',
                'price': '$29.99',
                'original_price': '$39.99',
                'discount': '25% off',
                'image_url': 'https://via.placeholder.com/300x300?text=Fashion+Item',
                'store': 'Amazon Fashion',
                'rating': 4.5,
                'reviews_count': 1250
            },
            {
                'title': f'{query} - Designer Style',
                'price': '$49.99',
                'original_price': '$79.99',
                'discount': '38% off',
                'image_url': 'https://via.placeholder.com/300x300?text=Designer+Item',
                'store': 'Amazon Fashion',
                'rating': 4.2,
                'reviews_count': 890
            }
        ]
        
        for item in mock_items[:max_results]:
            results.append(OutfitSearchResult(
                title=item['title'],
                price=item['price'],
                original_price=item.get('original_price'),
                discount=item.get('discount'),
                image_url=item['image_url'],
                product_url='https://amazon.com/fashion',
                store=item['store'],
                rating=item.get('rating'),
                reviews_count=item.get('reviews_count'),
                description=f"High-quality {query} perfect for any occasion",
                category="clothing"
            ))
        
        return results
    
    def _filter_by_price_range(self, results: List[OutfitSearchResult], price_range: tuple) -> List[OutfitSearchResult]:
        """Filter results by price range"""
        min_price, max_price = price_range
        filtered_results = []
        
        for result in results:
            try:
                # Extract numeric price
                price_str = re.search(r'\d+\.?\d*', result.price)
                if price_str:
                    price = float(price_str.group())
                    if min_price <= price <= max_price:
                        filtered_results.append(result)
            except:
                # If price parsing fails, include the item
                filtered_results.append(result)
        
        return filtered_results
    
    def _sort_results(self, results: List[OutfitSearchResult]) -> List[OutfitSearchResult]:
        """Sort results by relevance and price"""
        def sort_key(result):
            # Prioritize items with ratings, then by price
            rating_score = result.rating or 0
            try:
                price_num = float(re.search(r'\d+\.?\d*', result.price).group())
                # Lower price gets higher score (inverted)
                price_score = 1000 / (price_num + 1)
            except:
                price_score = 0
            
            return -(rating_score * 10 + price_score)
        
        return sorted(results, key=sort_key)
    
    def get_outfit_suggestions_by_category(self, style: str, categories: List[str]) -> Dict[str, List[OutfitSearchResult]]:
        """Get outfit suggestions organized by clothing category"""
        suggestions = {}
        
        for category in categories:
            query = f"{style} {category}"
            results = self.search_outfits(query, max_results=5)
            suggestions[category] = results
        
        return suggestions
