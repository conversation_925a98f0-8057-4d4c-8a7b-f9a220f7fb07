# Web Management Interface for Privacy VPN
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt flask-wtf

# Copy web application
COPY src/ ./src/
COPY web/ ./web/
COPY config/ ./config/

# Create web user
RUN useradd -r -s /bin/false webuser

# Expose web port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run web interface
USER webuser
CMD ["python", "web/app.py"]
