# Privacy-focused Unbound DNS Configuration
server:
    # Basic settings
    verbosity: 1
    interface: 0.0.0.0
    port: 53
    do-ip4: yes
    do-ip6: no
    do-udp: yes
    do-tcp: yes
    
    # Privacy settings
    hide-identity: yes
    hide-version: yes
    hide-trustanchor: yes
    
    # Security settings
    harden-glue: yes
    harden-dnssec-stripped: yes
    harden-below-nxdomain: yes
    harden-referral-path: yes
    use-caps-for-id: yes
    
    # Performance settings
    num-threads: 2
    msg-cache-slabs: 4
    rrset-cache-slabs: 4
    infra-cache-slabs: 4
    key-cache-slabs: 4
    
    # Cache settings
    cache-min-ttl: 3600
    cache-max-ttl: 86400
    prefetch: yes
    prefetch-key: yes
    
    # Privacy-focused upstream DNS servers
    # Cloudflare DNS (privacy-focused)
    forward-zone:
        name: "."
        forward-addr: *******@853#cloudflare-dns.com
        forward-addr: *******@853#cloudflare-dns.com
        forward-tls-upstream: yes
    
    # Block common tracking and ad domains
    local-zone: "doubleclick.net" refuse
    local-zone: "googleadservices.com" refuse
    local-zone: "googlesyndication.com" refuse
    local-zone: "googletagmanager.com" refuse
    local-zone: "google-analytics.com" refuse
    local-zone: "facebook.com" refuse
    local-zone: "fbcdn.net" refuse
    local-zone: "twitter.com" refuse
    local-zone: "t.co" refuse
    local-zone: "amazon-adsystem.com" refuse
    local-zone: "ads.yahoo.com" refuse
    local-zone: "bing.com" refuse
    
    # Additional privacy zones
    local-zone: "tracker.example.com" refuse
    local-zone: "analytics.example.com" refuse
