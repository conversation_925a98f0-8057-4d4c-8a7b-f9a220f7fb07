# Privacy-focused VPN Server Configuration

server:
  host: "0.0.0.0"
  port: 1194
  protocol: "udp"
  max_clients: 50
  keepalive_interval: 10
  keepalive_timeout: 120

network:
  subnet: "********/24"
  dns_servers:
    - "127.0.0.1"  # Local Unbound resolver
    - "*******"    # Cloudflare (fallback)
    - "*******"    # Quad9 (privacy-focused)
  routes: []
  redirect_gateway: true

security:
  cert_file: "/etc/vpn/certs/server.crt"
  key_file: "/etc/vpn/certs/server.key"
  ca_file: "/etc/vpn/certs/ca.crt"
  dh_file: "/etc/vpn/certs/dh.pem"
  cipher: "AES-256-GCM"
  auth: "SHA256"
  tls_version_min: "1.2"

# Privacy-specific settings
privacy:
  # Block tracking and ads
  block_ads: true
  block_trackers: true
  block_malware: true
  
  # DNS privacy
  dns_over_https: true
  dns_over_tls: true
  
  # Traffic obfuscation
  obfuscate_traffic: true
  random_padding: true
  
  # IP rotation (if multiple exit IPs available)
  rotate_exit_ip: false
  rotation_interval: 3600  # seconds
  
  # Logging privacy
  no_logs: true
  anonymize_logs: true

logging:
  level: "INFO"
  file: "/var/log/vpn/vpn-server.log"
  max_size: 10485760
  backup_count: 3
  
# Docker-specific settings
docker:
  container_name: "privacy-vpn-server"
  network_mode: "host"
  privileged: true
