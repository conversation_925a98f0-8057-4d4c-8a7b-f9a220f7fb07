# VPN Client Configuration

client:
  server_host: "127.0.0.1"     # VPN server hostname or IP
  server_port: 1194            # VPN server port
  protocol: "udp"              # Protocol: udp or tcp

security:
  cert_file: "config/certificates/client.crt"
  key_file: "config/certificates/client.key"
  ca_file: "config/certificates/ca.crt"
  cipher: "AES-256-GCM"        # Encryption cipher
  auth: "SHA256"               # Authentication algorithm
  tls_version_min: "1.2"       # Minimum TLS version

logging:
  level: "INFO"                # Log level: DEBUG, INFO, WARNING, ERROR
  file: "logs/vpn-client.log"  # Log file path
  max_size: 10485760          # Max log file size (10MB)
  backup_count: 5             # Number of backup log files
