"""
Main VPN Server implementation
"""

import asyncio
import socket
import threading
import time
from typing import Dict, <PERSON><PERSON>, Tuple
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import Config<PERSON>anager, VPNConfig
from ..utils.protocol import <PERSON>NPacket, PacketType, ProtocolHandler
from .client_manager import ClientManager
from .network_manager import NetworkManager

logger = get_logger(__name__)

class VPNServer:
    """Main VPN Server class"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_manager = ConfigManager(config_path)
        self.config: VPNConfig = self.config_manager.load_config()
        
        self.protocol_handler = ProtocolHandler()
        self.client_manager = ClientManager(self.config)
        self.network_manager = NetworkManager(self.config)
        
        self.socket: Optional[socket.socket] = None
        self.running = False
        self.server_thread: Optional[threading.Thread] = None
        
        logger.info("VPN Server initialized", 
                   host=self.config.server.host, 
                   port=self.config.server.port)
    
    def start(self) -> None:
        """Start the VPN server"""
        if self.running:
            logger.warning("Server is already running")
            return
        
        try:
            # Initialize network manager
            self.network_manager.setup()
            
            # Create and bind socket
            self._create_socket()
            
            # Start server thread
            self.running = True
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()
            
            # Start keepalive thread
            keepalive_thread = threading.Thread(target=self._keepalive_loop, daemon=True)
            keepalive_thread.start()
            
            logger.info("VPN Server started successfully")
            
        except Exception as e:
            logger.error("Failed to start VPN server", error=str(e))
            self.stop()
            raise
    
    def stop(self) -> None:
        """Stop the VPN server"""
        if not self.running:
            return
        
        logger.info("Stopping VPN server...")
        self.running = False
        
        # Close socket
        if self.socket:
            self.socket.close()
            self.socket = None
        
        # Disconnect all clients
        self.client_manager.disconnect_all()
        
        # Cleanup network
        self.network_manager.cleanup()
        
        # Wait for server thread
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5)
        
        logger.info("VPN Server stopped")
    
    def _create_socket(self) -> None:
        """Create and bind server socket"""
        if self.config.server.protocol.lower() == 'udp':
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        else:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.socket.bind((self.config.server.host, self.config.server.port))
        
        if self.config.server.protocol.lower() == 'tcp':
            self.socket.listen(self.config.server.max_clients)
        
        logger.info("Socket created and bound", 
                   protocol=self.config.server.protocol,
                   address=f"{self.config.server.host}:{self.config.server.port}")
    
    def _server_loop(self) -> None:
        """Main server loop"""
        logger.info("Server loop started")
        
        try:
            if self.config.server.protocol.lower() == 'udp':
                self._udp_server_loop()
            else:
                self._tcp_server_loop()
        except Exception as e:
            logger.error("Server loop error", error=str(e))
        finally:
            logger.info("Server loop ended")
    
    def _udp_server_loop(self) -> None:
        """UDP server loop"""
        self.socket.settimeout(1.0)  # Non-blocking with timeout
        
        while self.running:
            try:
                data, addr = self.socket.recvfrom(4096)
                if data:
                    self._handle_packet(data, addr)
            except socket.timeout:
                continue
            except Exception as e:
                if self.running:
                    logger.error("UDP receive error", error=str(e))
    
    def _tcp_server_loop(self) -> None:
        """TCP server loop"""
        self.socket.settimeout(1.0)
        
        while self.running:
            try:
                conn, addr = self.socket.accept()
                # Handle TCP connection in separate thread
                client_thread = threading.Thread(
                    target=self._handle_tcp_client,
                    args=(conn, addr),
                    daemon=True
                )
                client_thread.start()
            except socket.timeout:
                continue
            except Exception as e:
                if self.running:
                    logger.error("TCP accept error", error=str(e))
    
    def _handle_tcp_client(self, conn: socket.socket, addr: Tuple[str, int]) -> None:
        """Handle TCP client connection"""
        try:
            while self.running:
                data = conn.recv(4096)
                if not data:
                    break
                self._handle_packet(data, addr, conn)
        except Exception as e:
            logger.error("TCP client error", client=addr, error=str(e))
        finally:
            conn.close()
    
    def _handle_packet(self, data: bytes, addr: Tuple[str, int], 
                      conn: Optional[socket.socket] = None) -> None:
        """Handle incoming packet"""
        packet = VPNPacket.from_bytes(data)
        if not packet:
            logger.warning("Invalid packet received", client=addr)
            return
        
        logger.debug("Packet received", 
                    client=addr, 
                    type=packet.packet_type.name,
                    session=packet.session_id)
        
        try:
            if packet.packet_type == PacketType.HANDSHAKE_INIT:
                self._handle_handshake_init(packet, addr, conn)
            elif packet.packet_type == PacketType.AUTH_REQUEST:
                self._handle_auth_request(packet, addr, conn)
            elif packet.packet_type == PacketType.DATA:
                self._handle_data_packet(packet, addr, conn)
            elif packet.packet_type == PacketType.KEEPALIVE:
                self._handle_keepalive(packet, addr, conn)
            elif packet.packet_type == PacketType.DISCONNECT:
                self._handle_disconnect(packet, addr, conn)
            else:
                logger.warning("Unknown packet type", type=packet.packet_type)
                
        except Exception as e:
            logger.error("Packet handling error", error=str(e), client=addr)
    
    def _handle_handshake_init(self, packet: VPNPacket, addr: Tuple[str, int],
                              conn: Optional[socket.socket] = None) -> None:
        """Handle handshake initiation"""
        logger.info("Handshake initiated", client=addr, session=packet.session_id)
        
        # Process handshake and create client session
        client_id = self.client_manager.create_client(addr, packet.session_id, conn)
        
        if client_id:
            # Send handshake response
            response_packet = self.protocol_handler.create_packet(
                PacketType.HANDSHAKE_RESPONSE,
                packet.session_id,
                b"HANDSHAKE_OK"
            )
            self._send_packet(response_packet, addr, conn)
            logger.info("Handshake response sent", client=addr)
        else:
            logger.error("Failed to create client session", client=addr)
    
    def _handle_auth_request(self, packet: VPNPacket, addr: Tuple[str, int],
                           conn: Optional[socket.socket] = None) -> None:
        """Handle authentication request"""
        client = self.client_manager.get_client_by_address(addr)
        if not client:
            logger.warning("Auth request from unknown client", client=addr)
            return
        
        # Authenticate client (simplified for demo)
        auth_success = True  # TODO: Implement proper authentication
        
        if auth_success:
            # Assign IP and send configuration
            assigned_ip = self.network_manager.assign_ip(client.client_id)
            if assigned_ip:
                client.assigned_ip = assigned_ip
                config_data = self.network_manager.get_client_config(assigned_ip)
                
                config_packet = self.protocol_handler.create_config_push(
                    packet.session_id,
                    config_data
                )
                self._send_packet(config_packet, addr, conn)
                
                client.authenticated = True
                logger.info("Client authenticated", client=addr, ip=assigned_ip)
            else:
                logger.error("Failed to assign IP", client=addr)
        else:
            logger.warning("Authentication failed", client=addr)
    
    def _handle_data_packet(self, packet: VPNPacket, addr: Tuple[str, int],
                          conn: Optional[socket.socket] = None) -> None:
        """Handle data packet"""
        client = self.client_manager.get_client_by_address(addr)
        if not client or not client.authenticated:
            logger.warning("Data packet from unauthenticated client", client=addr)
            return
        
        # Process and route data packet
        self.network_manager.route_packet(packet.payload, client)
        client.update_activity()
    
    def _handle_keepalive(self, packet: VPNPacket, addr: Tuple[str, int],
                         conn: Optional[socket.socket] = None) -> None:
        """Handle keepalive packet"""
        client = self.client_manager.get_client_by_address(addr)
        if client:
            client.update_activity()
            
            # Send keepalive response
            response = self.protocol_handler.create_keepalive(packet.session_id)
            self._send_packet(response, addr, conn)
    
    def _handle_disconnect(self, packet: VPNPacket, addr: Tuple[str, int],
                          conn: Optional[socket.socket] = None) -> None:
        """Handle disconnect packet"""
        client = self.client_manager.get_client_by_address(addr)
        if client:
            self.client_manager.disconnect_client(client.client_id)
            logger.info("Client disconnected", client=addr)
    
    def _send_packet(self, packet: VPNPacket, addr: Tuple[str, int],
                    conn: Optional[socket.socket] = None) -> None:
        """Send packet to client"""
        try:
            data = packet.to_bytes()
            if conn:  # TCP
                conn.send(data)
            else:  # UDP
                self.socket.sendto(data, addr)
        except Exception as e:
            logger.error("Failed to send packet", client=addr, error=str(e))
    
    def _keepalive_loop(self) -> None:
        """Keepalive loop to check client connections"""
        while self.running:
            try:
                self.client_manager.check_timeouts()
                time.sleep(self.config.server.keepalive_interval)
            except Exception as e:
                logger.error("Keepalive loop error", error=str(e))
    
    def get_status(self) -> Dict:
        """Get server status"""
        return {
            'running': self.running,
            'clients': self.client_manager.get_client_count(),
            'config': {
                'host': self.config.server.host,
                'port': self.config.server.port,
                'protocol': self.config.server.protocol,
            }
        }
