"""
Client Manager for VPN Server
"""

import time
import uuid
from typing import Dict, Op<PERSON>, Tu<PERSON>, List
from dataclasses import dataclass, field
from threading import Lock
import socket

from ..utils.logger import get_logger
from ..utils.config import VPNConfig

logger = get_logger(__name__)

@dataclass
class VPNClient:
    """VPN Client information"""
    client_id: str
    address: Tuple[str, int]
    session_id: int
    connection: Optional[socket.socket] = None
    assigned_ip: Optional[str] = None
    authenticated: bool = False
    connected_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    bytes_sent: int = 0
    bytes_received: int = 0
    
    def update_activity(self) -> None:
        """Update last activity timestamp"""
        self.last_activity = time.time()
    
    def is_expired(self, timeout: int) -> bool:
        """Check if client connection has expired"""
        return time.time() - self.last_activity > timeout
    
    def get_connection_duration(self) -> float:
        """Get connection duration in seconds"""
        return time.time() - self.connected_at

class ClientManager:
    """Manages VPN client connections"""
    
    def __init__(self, config: VPNConfig):
        self.config = config
        self.clients: Dict[str, VPNClient] = {}
        self.address_to_client: Dict[Tuple[str, int], str] = {}
        self.session_to_client: Dict[int, str] = {}
        self.lock = Lock()
        
        logger.info("Client Manager initialized", max_clients=config.server.max_clients)
    
    def create_client(self, address: Tuple[str, int], session_id: int, 
                     connection: Optional[socket.socket] = None) -> Optional[str]:
        """Create a new client session"""
        with self.lock:
            # Check if we've reached max clients
            if len(self.clients) >= self.config.server.max_clients:
                logger.warning("Maximum clients reached", max_clients=self.config.server.max_clients)
                return None
            
            # Check if client already exists
            if address in self.address_to_client:
                existing_client_id = self.address_to_client[address]
                logger.info("Client reconnecting", client=address, client_id=existing_client_id)
                return existing_client_id
            
            # Create new client
            client_id = str(uuid.uuid4())
            client = VPNClient(
                client_id=client_id,
                address=address,
                session_id=session_id,
                connection=connection
            )
            
            # Store client mappings
            self.clients[client_id] = client
            self.address_to_client[address] = client_id
            self.session_to_client[session_id] = client_id
            
            logger.info("Client created", 
                       client=address, 
                       client_id=client_id, 
                       session=session_id,
                       total_clients=len(self.clients))
            
            return client_id
    
    def get_client(self, client_id: str) -> Optional[VPNClient]:
        """Get client by ID"""
        with self.lock:
            return self.clients.get(client_id)
    
    def get_client_by_address(self, address: Tuple[str, int]) -> Optional[VPNClient]:
        """Get client by address"""
        with self.lock:
            client_id = self.address_to_client.get(address)
            if client_id:
                return self.clients.get(client_id)
            return None
    
    def get_client_by_session(self, session_id: int) -> Optional[VPNClient]:
        """Get client by session ID"""
        with self.lock:
            client_id = self.session_to_client.get(session_id)
            if client_id:
                return self.clients.get(client_id)
            return None
    
    def disconnect_client(self, client_id: str) -> bool:
        """Disconnect a client"""
        with self.lock:
            client = self.clients.get(client_id)
            if not client:
                return False
            
            # Close connection if TCP
            if client.connection:
                try:
                    client.connection.close()
                except:
                    pass
            
            # Remove from mappings
            self.address_to_client.pop(client.address, None)
            self.session_to_client.pop(client.session_id, None)
            self.clients.pop(client_id, None)
            
            duration = client.get_connection_duration()
            logger.info("Client disconnected", 
                       client=client.address,
                       client_id=client_id,
                       duration=f"{duration:.1f}s",
                       bytes_sent=client.bytes_sent,
                       bytes_received=client.bytes_received,
                       remaining_clients=len(self.clients))
            
            return True
    
    def disconnect_all(self) -> None:
        """Disconnect all clients"""
        with self.lock:
            client_ids = list(self.clients.keys())
            for client_id in client_ids:
                self.disconnect_client(client_id)
            
            logger.info("All clients disconnected")
    
    def check_timeouts(self) -> None:
        """Check for expired client connections"""
        timeout = self.config.server.keepalive_timeout
        expired_clients = []
        
        with self.lock:
            for client_id, client in self.clients.items():
                if client.is_expired(timeout):
                    expired_clients.append(client_id)
        
        # Disconnect expired clients
        for client_id in expired_clients:
            logger.info("Client connection expired", client_id=client_id)
            self.disconnect_client(client_id)
    
    def get_client_count(self) -> int:
        """Get number of connected clients"""
        with self.lock:
            return len(self.clients)
    
    def get_client_list(self) -> List[Dict]:
        """Get list of connected clients"""
        with self.lock:
            clients = []
            for client in self.clients.values():
                clients.append({
                    'client_id': client.client_id,
                    'address': f"{client.address[0]}:{client.address[1]}",
                    'assigned_ip': client.assigned_ip,
                    'authenticated': client.authenticated,
                    'connected_at': client.connected_at,
                    'last_activity': client.last_activity,
                    'duration': client.get_connection_duration(),
                    'bytes_sent': client.bytes_sent,
                    'bytes_received': client.bytes_received,
                })
            return clients
    
    def update_client_stats(self, client_id: str, bytes_sent: int = 0, bytes_received: int = 0) -> None:
        """Update client traffic statistics"""
        with self.lock:
            client = self.clients.get(client_id)
            if client:
                client.bytes_sent += bytes_sent
                client.bytes_received += bytes_received
                client.update_activity()
