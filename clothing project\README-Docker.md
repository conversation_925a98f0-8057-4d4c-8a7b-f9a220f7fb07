# 🐳 Personal Closet Website - Docker Setup

Run your AI-powered personal wardrobe manager in Docker for easy deployment and consistent environment!

## 🚀 Quick Start

### Option 1: One-Click Launch (Recommended)

**Windows:**
```bash
run-docker.bat
```

**Linux/Mac:**
```bash
chmod +x run-docker.sh
./run-docker.sh
```

### Option 2: Manual Docker Commands

```bash
# Build and start the application
docker-compose up --build

# Run in background
docker-compose up -d --build

# Stop the application
docker-compose down
```

## 📋 Prerequisites

1. **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop)
2. **Docker Compose** (included with Docker Desktop)

## 🌐 Access Your Website

Once running, open your browser to:
- **Main Application:** http://localhost:5000
- **Development Mode:** http://localhost:5001 (if using dev profile)

## 📁 Data Persistence

Your closet data and photos are automatically saved in:
- `./data/` - Application data
- `./static/uploads/` - Uploaded photos
- `./personal_closet.json` - Your closet database

These folders are mounted as volumes, so your data persists even when containers are restarted.

## 🛠️ Development Mode

For development with hot reload:

```bash
# Start development environment
docker-compose --profile dev up --build

# Or specifically run dev service
docker-compose up personal-closet-dev --build
```

Development features:
- ✅ Hot reload on code changes
- ✅ Debug mode enabled
- ✅ Source code mounted as volume
- ✅ Available on port 5001

## 🔧 Docker Commands Reference

```bash
# View running containers
docker-compose ps

# View logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# Restart services
docker-compose restart

# Stop and remove containers
docker-compose down

# Stop and remove containers + volumes
docker-compose down -v

# Rebuild without cache
docker-compose build --no-cache

# Run commands inside container
docker-compose exec personal-closet bash
```

## 🏗️ Architecture

```
┌─────────────────────────────────────┐
│           Docker Container          │
├─────────────────────────────────────┤
│  🐍 Python 3.11 + Flask            │
│  📱 Personal Closet Web App         │
│  🤖 AI Outfit Generator             │
│  📸 Photo Upload System             │
└─────────────────────────────────────┘
           │
           ▼
┌─────────────────────────────────────┐
│        Persistent Storage           │
├─────────────────────────────────────┤
│  📁 ./data/ (app data)              │
│  🖼️  ./static/uploads/ (photos)     │
│  💾 ./personal_closet.json (DB)     │
└─────────────────────────────────────┘
```

## 🔒 Security Features

- ✅ Non-root user inside container
- ✅ Minimal base image (Python slim)
- ✅ Health checks enabled
- ✅ Secure file permissions
- ✅ No sensitive data in image

## 🚨 Troubleshooting

### Container won't start
```bash
# Check Docker is running
docker info

# View detailed logs
docker-compose logs personal-closet

# Rebuild from scratch
docker-compose down
docker-compose build --no-cache
docker-compose up
```

### Port already in use
```bash
# Check what's using port 5000
netstat -tulpn | grep :5000

# Use different port
docker-compose up -p 5001:5000
```

### Permission issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER ./data ./static/uploads
chmod -R 755 ./data ./static/uploads
```

### Can't access website
- ✅ Check Docker container is running: `docker-compose ps`
- ✅ Verify port mapping: Container port 5000 → Host port 5000
- ✅ Try accessing: http://localhost:5000 or http://127.0.0.1:5000
- ✅ Check firewall settings

## 🌟 Benefits of Docker Version

- **🔄 Consistent Environment** - Works the same everywhere
- **📦 Easy Deployment** - One command to run everything
- **🔒 Isolated** - Doesn't interfere with your system
- **💾 Persistent Data** - Your closet data is always saved
- **🚀 Quick Setup** - No need to install Python/dependencies
- **🔧 Easy Updates** - Just rebuild the container

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure Docker Desktop is running
3. Try rebuilding with `--no-cache` flag
4. Check container logs for error messages

---

**Ready to revolutionize your wardrobe with Docker?** 🐳👗

Just run the startup script and start building your digital closet!
