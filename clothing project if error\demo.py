#!/usr/bin/env python3
"""
Demo script for the Stylish Clothing Generator
Shows various features and capabilities of the outfit generator
"""

from clothing_generator import OutfitGenerator, Occasion, Weather, Style
from advanced_features import (
    WeatherIntelligence, SeasonalRecommendations, 
    OutfitRatingSystem, WeatherCondition
)


def print_separator(title: str):
    """Print a formatted separator"""
    print("\n" + "="*60)
    print(f"🌟 {title.upper()} 🌟")
    print("="*60)


def print_outfit_details(outfit, number: int):
    """Print detailed outfit information"""
    print(f"\n👗 OUTFIT #{number}")
    print("-" * 25)
    print(f"📝 {outfit.description}")
    print(f"⭐ Overall Score: {outfit.overall_score:.1%}")
    print(f"🎨 Color Harmony: {outfit.color_harmony:.1%}")
    print(f"💫 Style Score: {outfit.style_score:.1%}")
    print(f"🎯 Occasion Fit: {outfit.occasion_fit:.1%}")
    
    print("\n👕 Items:")
    for item in outfit.items:
        colors_str = ", ".join(item.colors)
        styles_str = ", ".join([s.value for s in item.style])
        print(f"  • {item.name}")
        print(f"    Colors: {colors_str}")
        print(f"    Styles: {styles_str}")
        print(f"    Formality: {item.formality}/10")


def demo_basic_generation():
    """Demonstrate basic outfit generation"""
    print_separator("Basic Outfit Generation")
    
    generator = OutfitGenerator()
    
    # Generate casual outfits for mild weather
    print("🎯 Generating casual outfits for mild weather...")
    outfits = generator.generate_outfit(
        occasion=Occasion.CASUAL,
        weather=Weather.MILD,
        num_suggestions=2
    )
    
    for i, outfit in enumerate(outfits, 1):
        print_outfit_details(outfit, i)


def demo_style_preferences():
    """Demonstrate style-specific generation"""
    print_separator("Style-Specific Generation")
    
    generator = OutfitGenerator()
    
    styles_to_demo = [Style.CLASSIC, Style.EDGY, Style.MINIMALIST]
    
    for style in styles_to_demo:
        print(f"\n🎨 Generating {style.value.title()} style outfit for business occasion...")
        outfits = generator.generate_outfit(
            occasion=Occasion.BUSINESS,
            weather=Weather.COOL,
            preferred_style=style,
            num_suggestions=1
        )
        
        if outfits:
            print_outfit_details(outfits[0], 1)
        else:
            print("❌ No suitable outfits found for this combination")


def demo_weather_intelligence():
    """Demonstrate weather intelligence features"""
    print_separator("Weather Intelligence")
    
    # Create sample weather conditions
    weather_conditions = [
        WeatherCondition(temperature=30, humidity=70, wind_speed=10, precipitation=False, season="summer"),
        WeatherCondition(temperature=5, humidity=60, wind_speed=25, precipitation=False, season="winter"),
        WeatherCondition(temperature=18, humidity=85, wind_speed=15, precipitation=True, season="spring")
    ]
    
    for i, condition in enumerate(weather_conditions, 1):
        print(f"\n🌤️ Weather Condition #{i}:")
        print(f"   Temperature: {condition.temperature}°C")
        print(f"   Humidity: {condition.humidity}%")
        print(f"   Wind Speed: {condition.wind_speed} km/h")
        print(f"   Precipitation: {'Yes' if condition.precipitation else 'No'}")
        
        weather_category = WeatherIntelligence.determine_weather_category(condition)
        print(f"   Determined Category: {weather_category.value.title()}")
        
        recommendations = WeatherIntelligence.get_weather_recommendations(weather_category)
        if recommendations:
            print(f"   Recommended Fabrics: {', '.join(recommendations.get('fabrics', []))}")
            print(f"   Recommended Colors: {', '.join(recommendations.get('colors', []))}")
            print(f"   Tips: {'; '.join(recommendations.get('tips', []))}")


def demo_seasonal_recommendations():
    """Demonstrate seasonal recommendations"""
    print_separator("Seasonal Recommendations")
    
    seasons = ['spring', 'summer', 'autumn', 'winter']
    
    for season in seasons:
        print(f"\n🍃 {season.title()} Recommendations:")
        recs = SeasonalRecommendations.get_seasonal_recommendations(season)
        print(f"   Colors: {', '.join(recs['colors'][:5])}...")  # Show first 5
        print(f"   Styles: {', '.join(recs['styles'])}")
    
    current_season = SeasonalRecommendations.get_current_season()
    print(f"\n📅 Current Season: {current_season.title()}")


def demo_outfit_rating():
    """Demonstrate detailed outfit rating"""
    print_separator("Detailed Outfit Rating")
    
    generator = OutfitGenerator()
    
    # Generate an outfit for rating
    outfits = generator.generate_outfit(
        occasion=Occasion.DATE,
        weather=Weather.WARM,
        preferred_style=Style.ROMANTIC,
        num_suggestions=1
    )
    
    if outfits:
        outfit = outfits[0]
        print("🎯 Rating this outfit:")
        print_outfit_details(outfit, 1)
        
        # Get detailed ratings
        current_season = SeasonalRecommendations.get_current_season()
        detailed_ratings = OutfitRatingSystem.rate_outfit_detailed(
            outfit, Weather.WARM, Occasion.DATE, current_season
        )
        
        print("\n📊 Detailed Rating Breakdown:")
        for criterion, score in detailed_ratings.items():
            print(f"   {criterion.replace('_', ' ').title()}: {score:.1%}")
        
        # Calculate weighted score
        weighted_score = OutfitRatingSystem.calculate_weighted_score(detailed_ratings)
        print(f"\n⭐ Weighted Overall Score: {weighted_score:.1%}")
        
        # Get improvement suggestions
        suggestions = OutfitRatingSystem.get_improvement_suggestions(detailed_ratings)
        if suggestions:
            print("\n💡 Improvement Suggestions:")
            for suggestion in suggestions:
                print(f"   • {suggestion}")
        else:
            print("\n✨ This outfit is already well-coordinated!")


def demo_color_combinations():
    """Demonstrate color harmony analysis"""
    print_separator("Color Harmony Analysis")
    
    from clothing_generator import ColorHarmony
    
    color_combinations = [
        ['black', 'white'],
        ['navy', 'coral'],
        ['burgundy', 'cream'],
        ['red', 'green', 'blue'],  # Poor combination
        ['beige', 'brown', 'cream'],  # Good earth tones
    ]
    
    print("🎨 Testing various color combinations:")
    
    for i, colors in enumerate(color_combinations, 1):
        harmony_score = ColorHarmony.calculate_harmony(colors)
        colors_str = ", ".join(colors)
        
        if harmony_score >= 0.8:
            rating = "Excellent 💎"
        elif harmony_score >= 0.7:
            rating = "Great ✨"
        elif harmony_score >= 0.6:
            rating = "Good 👍"
        else:
            rating = "Needs work 🤔"
        
        print(f"   {i}. [{colors_str}] - {harmony_score:.1%} ({rating})")


def demo_occasion_matching():
    """Demonstrate occasion-specific outfit generation"""
    print_separator("Occasion-Specific Outfits")
    
    generator = OutfitGenerator()
    
    occasions_to_demo = [
        (Occasion.BUSINESS, "Professional meeting"),
        (Occasion.PARTY, "Evening celebration"),
        (Occasion.CASUAL, "Weekend brunch"),
        (Occasion.FORMAL, "Wedding guest")
    ]
    
    for occasion, description in occasions_to_demo:
        print(f"\n🎯 {description} ({occasion.value.title()}):")
        outfits = generator.generate_outfit(
            occasion=occasion,
            weather=Weather.MILD,
            num_suggestions=1
        )
        
        if outfits:
            outfit = outfits[0]
            print(f"   Suggestion: {outfit.description}")
            print(f"   Score: {outfit.overall_score:.1%}")
            print(f"   Items: {', '.join([item.name for item in outfit.items])}")
        else:
            print("   ❌ No suitable outfits found")


def main():
    """Run all demonstrations"""
    print("🌟 Welcome to the Stylish Clothing Generator Demo! 🌟")
    print("This demo showcases the various features and capabilities.")
    
    try:
        demo_basic_generation()
        demo_style_preferences()
        demo_weather_intelligence()
        demo_seasonal_recommendations()
        demo_outfit_rating()
        demo_color_combinations()
        demo_occasion_matching()
        
        print_separator("Demo Complete")
        print("✨ Thank you for exploring the Stylish Clothing Generator!")
        print("💡 Run 'python main.py' to use the interactive interface.")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        print("Please check that all files are present and properly configured.")


if __name__ == "__main__":
    main()
