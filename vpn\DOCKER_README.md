# Privacy VPN with Docker 🛡️

A privacy-focused VPN solution that helps you browse the web anonymously without being tracked. Built with Dock<PERSON> for easy deployment and maximum privacy protection.

## 🌟 Privacy Features

- **🔒 Strong Encryption**: AES-256-GCM encryption with RSA-4096 key exchange
- **🚫 Ad & Tracker Blocking**: Built-in blocklists to prevent tracking and ads
- **🌐 DNS Privacy**: DNS-over-HTTPS and DNS-over-TLS support
- **🛡️ Firewall Protection**: Advanced iptables rules for traffic filtering
- **📝 No-Logs Policy**: No connection logs or user activity tracking
- **🔄 Traffic Obfuscation**: Makes VPN traffic harder to detect
- **🌍 Anonymous Browsing**: Hide your real IP address and location

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Linux host with root access (for network configuration)
- Open port 1194/UDP for VPN connections

### 1. Deploy the VPN Server

```bash
# Clone or download the VPN files
cd vpn

# Make deployment script executable
chmod +x deploy.sh

# Deploy the privacy VPN
./deploy.sh
```

### 2. Access Management Interface

- **Web Interface**: http://localhost:8080
- **Monitoring**: http://localhost:3000 (admin/vpnadmin)

### 3. Generate Client Configuration

```bash
# Generate client certificates
docker-compose exec privacy-vpn python cli/server_cli.py generate-certs --client

# Get client configuration
docker-compose exec privacy-vpn cat /etc/vpn/client.ovpn > client.ovpn
```

### 4. Connect Your Devices

Use the generated `client.ovpn` file with any OpenVPN-compatible client:

- **Windows**: OpenVPN GUI
- **macOS**: Tunnelblick
- **Linux**: OpenVPN client
- **Android**: OpenVPN Connect
- **iOS**: OpenVPN Connect

## 🔧 Configuration

### Environment Variables

```bash
# Server configuration
VPN_SERVER_HOST=0.0.0.0
VPN_SERVER_PORT=1194
VPN_PROTOCOL=udp

# Privacy settings
PRIVACY_MODE=true
DNS_BLOCKING=true
LOG_LEVEL=INFO
```

### Custom Configuration

Edit `docker/privacy-config.yaml` to customize:

```yaml
privacy:
  block_ads: true
  block_trackers: true
  block_malware: true
  dns_over_https: true
  obfuscate_traffic: true
  no_logs: true
```

## 📊 Monitoring

### Web Dashboard

Access the web interface at http://localhost:8080 to:

- View connected clients
- Monitor privacy statistics
- Configure blocking rules
- Check server status

### Grafana Monitoring

Access Grafana at http://localhost:3000 for:

- Connection metrics
- Bandwidth usage
- Privacy protection stats
- System performance

## 🛠️ Management Commands

```bash
# View service status
docker-compose ps

# View logs
docker-compose logs -f privacy-vpn

# Restart services
docker-compose restart

# Stop services
docker-compose down

# Update and restart
docker-compose pull && docker-compose up -d

# Access VPN server shell
docker-compose exec privacy-vpn bash
```

## 🔐 Security Best Practices

### 1. Change Default Passwords

```bash
# Update web interface password
export SECRET_KEY="your-secure-secret-key"

# Update Grafana admin password
export GF_SECURITY_ADMIN_PASSWORD="your-secure-password"
```

### 2. Use Strong Certificates

```bash
# Generate new certificates with custom settings
docker-compose exec privacy-vpn python cli/server_cli.py generate-certs \
  --key-size 4096 \
  --days 365 \
  --country US \
  --organization "Your Organization"
```

### 3. Regular Updates

```bash
# Update blocklists and security rules
docker-compose exec privacy-vpn python docker/privacy_monitor.py --update-blocklists
```

## 🌐 Client Setup Examples

### Windows OpenVPN GUI

1. Download and install OpenVPN GUI
2. Copy `client.ovpn` to `C:\Program Files\OpenVPN\config\`
3. Right-click OpenVPN GUI → Connect

### Linux Command Line

```bash
# Install OpenVPN
sudo apt-get install openvpn

# Connect using configuration
sudo openvpn --config client.ovpn
```

### macOS Tunnelblick

1. Install Tunnelblick
2. Double-click `client.ovpn` to import
3. Connect from Tunnelblick menu

## 🔍 Troubleshooting

### VPN Server Not Starting

```bash
# Check logs
docker-compose logs privacy-vpn

# Verify TUN device
ls -la /dev/net/tun

# Check privileges
docker-compose exec privacy-vpn ip tuntap show
```

### DNS Not Working

```bash
# Check DNS resolver
docker-compose exec privacy-vpn nslookup google.com 127.0.0.1

# Restart DNS service
docker-compose restart privacy-vpn
```

### Connection Issues

```bash
# Check firewall rules
docker-compose exec privacy-vpn iptables -L -n

# Test connectivity
docker-compose exec privacy-vpn netstat -tuln | grep 1194
```

### Privacy Features Not Working

```bash
# Check privacy monitor
docker-compose logs privacy-vpn | grep "Privacy Monitor"

# Verify blocklists
docker-compose exec privacy-vpn cat /etc/unbound/unbound.conf | grep refuse
```

## 📈 Performance Tuning

### For High Traffic

```yaml
# In docker-compose.yml
services:
  privacy-vpn:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
```

### For Multiple Clients

```yaml
# In privacy-config.yaml
server:
  max_clients: 100
  keepalive_interval: 30
```

## 🆘 Support

### Common Issues

1. **Port 1194 already in use**: Change port in configuration
2. **Permission denied**: Ensure Docker has proper privileges
3. **DNS leaks**: Verify DNS configuration in client
4. **Slow speeds**: Check server resources and network

### Getting Help

- Check logs: `docker-compose logs -f`
- Test connectivity: Use built-in diagnostics
- Monitor resources: Use Grafana dashboard

## 🔄 Updates

```bash
# Update to latest version
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

## 📄 License

This privacy VPN solution is provided as-is for educational and privacy protection purposes. Use responsibly and in accordance with local laws.

---

**🛡️ Stay Private, Stay Secure!**
