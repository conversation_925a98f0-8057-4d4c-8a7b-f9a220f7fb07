#!/usr/bin/env python3
"""
Privacy VPN Web Management Interface
"""

import os
import sys
from pathlib import Path
from flask import Flask, render_template, jsonify, request, redirect, url_for, flash
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, IPAddress
import subprocess
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.logger import get_logger
from src.utils.config import ConfigManager

logger = get_logger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'privacy-vpn-secret-key-change-me')

class VPNConfigForm(FlaskForm):
    """Form for VPN configuration"""
    server_host = String<PERSON>ield('Server Host', validators=[DataRequired()])
    server_port = StringField('Server Port', validators=[DataRequired()])
    protocol = SelectField('Protocol', choices=[('udp', 'UDP'), ('tcp', 'TCP')])
    max_clients = StringField('Max Clients', validators=[DataRequired()])
    dns_blocking = BooleanField('Enable DNS Blocking')
    ad_blocking = BooleanField('Enable Ad Blocking')
    tracker_blocking = BooleanField('Enable Tracker Blocking')
    submit = SubmitField('Update Configuration')

@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get VPN server status
        status = get_vpn_status()
        
        # Get connected clients
        clients = get_connected_clients()
        
        # Get privacy stats
        privacy_stats = get_privacy_stats()
        
        return render_template('dashboard.html', 
                             status=status, 
                             clients=clients,
                             privacy_stats=privacy_stats)
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        return render_template('error.html', error=str(e))

@app.route('/config', methods=['GET', 'POST'])
def config():
    """Configuration management"""
    form = VPNConfigForm()
    
    if form.validate_on_submit():
        try:
            # Update configuration
            update_config(form.data)
            flash('Configuration updated successfully!', 'success')
            return redirect(url_for('config'))
        except Exception as e:
            flash(f'Configuration update failed: {e}', 'error')
    
    # Load current configuration
    try:
        current_config = load_current_config()
        if current_config:
            form.server_host.data = current_config.get('server', {}).get('host', '0.0.0.0')
            form.server_port.data = str(current_config.get('server', {}).get('port', 1194))
            form.protocol.data = current_config.get('server', {}).get('protocol', 'udp')
            form.max_clients.data = str(current_config.get('server', {}).get('max_clients', 50))
            form.dns_blocking.data = current_config.get('privacy', {}).get('block_ads', False)
            form.ad_blocking.data = current_config.get('privacy', {}).get('block_ads', False)
            form.tracker_blocking.data = current_config.get('privacy', {}).get('block_trackers', False)
    except Exception as e:
        flash(f'Failed to load configuration: {e}', 'error')
    
    return render_template('config.html', form=form)

@app.route('/clients')
def clients():
    """Client management"""
    try:
        clients = get_connected_clients()
        return render_template('clients.html', clients=clients)
    except Exception as e:
        logger.error(f"Clients page error: {e}")
        return render_template('error.html', error=str(e))

@app.route('/privacy')
def privacy():
    """Privacy dashboard"""
    try:
        privacy_stats = get_privacy_stats()
        blocked_domains = get_blocked_domains()
        return render_template('privacy.html', 
                             privacy_stats=privacy_stats,
                             blocked_domains=blocked_domains)
    except Exception as e:
        logger.error(f"Privacy page error: {e}")
        return render_template('error.html', error=str(e))

@app.route('/api/status')
def api_status():
    """API endpoint for VPN status"""
    try:
        status = get_vpn_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clients')
def api_clients():
    """API endpoint for connected clients"""
    try:
        clients = get_connected_clients()
        return jsonify(clients)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/privacy-stats')
def api_privacy_stats():
    """API endpoint for privacy statistics"""
    try:
        stats = get_privacy_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'privacy-vpn-web'})

def get_vpn_status():
    """Get VPN server status"""
    try:
        result = subprocess.run(['python', 'cli/server_cli.py', 'status'], 
                              capture_output=True, text=True, cwd='/app')
        
        if result.returncode == 0:
            return {
                'status': 'running',
                'message': 'VPN server is running',
                'uptime': 'Unknown'  # Would need to implement uptime tracking
            }
        else:
            return {
                'status': 'stopped',
                'message': 'VPN server is not running',
                'uptime': None
            }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Status check failed: {e}',
            'uptime': None
        }

def get_connected_clients():
    """Get list of connected clients"""
    try:
        result = subprocess.run(['python', 'cli/server_cli.py', 'clients'], 
                              capture_output=True, text=True, cwd='/app')
        
        if result.returncode == 0:
            # Parse client list (would need to implement proper parsing)
            return [
                {
                    'id': 'client-1',
                    'ip': '********',
                    'connected_at': '2024-01-01 12:00:00',
                    'bytes_sent': 1024000,
                    'bytes_received': 2048000
                }
            ]
        else:
            return []
    except Exception as e:
        logger.error(f"Failed to get clients: {e}")
        return []

def get_privacy_stats():
    """Get privacy protection statistics"""
    try:
        return {
            'blocked_requests': 1234,
            'blocked_ads': 567,
            'blocked_trackers': 890,
            'dns_queries': 5678,
            'privacy_score': 95
        }
    except Exception as e:
        logger.error(f"Failed to get privacy stats: {e}")
        return {}

def get_blocked_domains():
    """Get list of blocked domains"""
    try:
        # Read from blocklist file or database
        return [
            'doubleclick.net',
            'googleadservices.com',
            'facebook.com',
            'tracker.example.com'
        ]
    except Exception as e:
        logger.error(f"Failed to get blocked domains: {e}")
        return []

def load_current_config():
    """Load current VPN configuration"""
    try:
        config_path = '/etc/vpn/privacy-config.yaml'
        if os.path.exists(config_path):
            config_manager = ConfigManager()
            return config_manager.load_config(config_path)
        return None
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return None

def update_config(form_data):
    """Update VPN configuration"""
    try:
        # This would update the actual configuration
        logger.info(f"Updating configuration: {form_data}")
        # Implementation would save to config file and restart services
        pass
    except Exception as e:
        logger.error(f"Failed to update config: {e}")
        raise

if __name__ == '__main__':
    port = int(os.environ.get('WEB_PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
