"""
Configuration management for VPN implementation
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from .logger import get_logger

logger = get_logger(__name__)

@dataclass
class ServerConfig:
    """Server configuration settings"""
    host: str = "0.0.0.0"
    port: int = 1194
    protocol: str = "udp"
    max_clients: int = 100
    keepalive_interval: int = 10
    keepalive_timeout: int = 120
    
@dataclass
class NetworkConfig:
    """Network configuration settings"""
    subnet: str = "********/24"
    dns_servers: list = field(default_factory=lambda: ["*******", "*******"])
    routes: list = field(default_factory=list)
    redirect_gateway: bool = True
    
@dataclass
class SecurityConfig:
    """Security configuration settings"""
    cert_file: str = "config/certificates/server.crt"
    key_file: str = "config/certificates/server.key"
    ca_file: str = "config/certificates/ca.crt"
    dh_file: str = "config/certificates/dh.pem"
    cipher: str = "AES-256-GCM"
    auth: str = "SHA256"
    tls_version_min: str = "1.2"
    
@dataclass
class LoggingConfig:
    """Logging configuration settings"""
    level: str = "INFO"
    file: Optional[str] = None
    max_size: int = 10485760  # 10MB
    backup_count: int = 5
    
@dataclass
class VPNConfig:
    """Main VPN configuration"""
    server: ServerConfig = field(default_factory=ServerConfig)
    network: NetworkConfig = field(default_factory=NetworkConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

class ConfigManager:
    """Configuration manager for loading and saving VPN settings"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path("config/server.yaml")
        self.config: Optional[VPNConfig] = None
        
    def load_config(self) -> VPNConfig:
        """Load configuration from file"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    
                self.config = self._dict_to_config(data)
                logger.info("Configuration loaded", path=str(self.config_path))
            else:
                logger.warning("Config file not found, using defaults", path=str(self.config_path))
                self.config = VPNConfig()
                self.save_config()  # Save default config
                
        except Exception as e:
            logger.error("Failed to load configuration", error=str(e))
            self.config = VPNConfig()
            
        return self.config
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        if not self.config:
            return
            
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            data = self._config_to_dict(self.config)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, indent=2)
                
            logger.info("Configuration saved", path=str(self.config_path))
            
        except Exception as e:
            logger.error("Failed to save configuration", error=str(e))
    
    def _dict_to_config(self, data: Dict[str, Any]) -> VPNConfig:
        """Convert dictionary to VPNConfig object"""
        config = VPNConfig()
        
        if 'server' in data:
            server_data = data['server']
            config.server = ServerConfig(**{k: v for k, v in server_data.items() 
                                         if hasattr(ServerConfig, k)})
        
        if 'network' in data:
            network_data = data['network']
            config.network = NetworkConfig(**{k: v for k, v in network_data.items() 
                                            if hasattr(NetworkConfig, k)})
        
        if 'security' in data:
            security_data = data['security']
            config.security = SecurityConfig(**{k: v for k, v in security_data.items() 
                                              if hasattr(SecurityConfig, k)})
        
        if 'logging' in data:
            logging_data = data['logging']
            config.logging = LoggingConfig(**{k: v for k, v in logging_data.items() 
                                            if hasattr(LoggingConfig, k)})
        
        return config
    
    def _config_to_dict(self, config: VPNConfig) -> Dict[str, Any]:
        """Convert VPNConfig object to dictionary"""
        return {
            'server': {
                'host': config.server.host,
                'port': config.server.port,
                'protocol': config.server.protocol,
                'max_clients': config.server.max_clients,
                'keepalive_interval': config.server.keepalive_interval,
                'keepalive_timeout': config.server.keepalive_timeout,
            },
            'network': {
                'subnet': config.network.subnet,
                'dns_servers': config.network.dns_servers,
                'routes': config.network.routes,
                'redirect_gateway': config.network.redirect_gateway,
            },
            'security': {
                'cert_file': config.security.cert_file,
                'key_file': config.security.key_file,
                'ca_file': config.security.ca_file,
                'dh_file': config.security.dh_file,
                'cipher': config.security.cipher,
                'auth': config.security.auth,
                'tls_version_min': config.security.tls_version_min,
            },
            'logging': {
                'level': config.logging.level,
                'file': config.logging.file,
                'max_size': config.logging.max_size,
                'backup_count': config.logging.backup_count,
            }
        }
