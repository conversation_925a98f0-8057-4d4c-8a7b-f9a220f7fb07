#!/usr/bin/env python3
"""
Personal Closet Website Launcher
Easy startup script for the personal closet web application
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['flask', 'werkzeug']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package.title()} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package.title()} is not installed")
    
    return missing_packages

def install_dependencies(missing_packages):
    """Install missing dependencies"""
    if not missing_packages:
        return True
    
    print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            '-r', 'requirements.txt'
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("\nTry running manually:")
        print("pip install -r requirements.txt")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = [
        'static/uploads',
        'templates'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Directory ready: {directory}")

def check_files():
    """Check if required files exist"""
    required_files = [
        'app.py',
        'personal_closet.py',
        'clothing_generator.py',
        'templates/base.html',
        'templates/index.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ Missing file: {file_path}")
        else:
            print(f"✅ File exists: {file_path}")
    
    return len(missing_files) == 0

def start_application():
    """Start the Flask application"""
    print("\n🚀 Starting Personal Closet Website...")
    print("📱 The website will open in your browser automatically")
    print("🔗 Manual URL: http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop the server\n")
    
    # Open browser after a short delay
    import threading
    def open_browser():
        import time
        time.sleep(2)  # Wait for server to start
        webbrowser.open('http://localhost:5000')
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Start Flask app
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 Personal Closet Website stopped")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    print("👗 Personal Closet Website Launcher")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check if required files exist
    print("\n📁 Checking required files...")
    if not check_files():
        print("\n❌ Some required files are missing. Please ensure all files are in place.")
        return False
    
    # Setup directories
    print("\n📂 Setting up directories...")
    setup_directories()
    
    # Check dependencies
    print("\n🔍 Checking dependencies...")
    missing_packages = check_dependencies()
    
    # Install missing dependencies
    if missing_packages:
        install_choice = input(f"\nInstall missing packages? (y/n): ").lower().strip()
        if install_choice in ['y', 'yes']:
            if not install_dependencies(missing_packages):
                return False
        else:
            print("❌ Cannot start without required dependencies")
            return False
    
    # Start the application
    print("\n" + "=" * 40)
    return start_application()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nPress Enter to exit...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        input("\nPress Enter to exit...")
        sys.exit(1)
