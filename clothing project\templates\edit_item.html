{% extends "base.html" %}

{% block title %}Edit Item - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-edit me-3"></i>Edit Item</h1>
    <p>Update your clothing item details</p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="editItemForm">
                    <!-- Photo Upload -->
                    <div class="mb-4">
                        <label for="photo" class="form-label">
                            <i class="fas fa-camera me-2"></i>Item Photo
                        </label>
                        <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                            <div id="photoPreview" class="photo-preview">
                                {% if item.photo_filename %}
                                <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                     alt="{{ item.name }}" class="preview-image">
                                <p class="text-muted mt-2">Click to change photo</p>
                                {% else %}
                                <i class="fas fa-camera fa-3x text-muted mb-2"></i>
                                <p class="text-muted">Click to upload photo</p>
                                <small class="text-muted">Supports: JPG, PNG, GIF, WebP (Max 16MB)</small>
                                {% endif %}
                            </div>
                        </div>
                        <input type="file" class="form-control d-none" id="photo" name="photo" 
                               accept="image/*" onchange="previewPhoto(this)">
                    </div>

                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="name" class="form-label">Item Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   value="{{ item.name }}" placeholder="e.g., Blue Denim Jacket">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="top" {{ 'selected' if item.category == 'top' else '' }}>Top</option>
                                <option value="bottom" {{ 'selected' if item.category == 'bottom' else '' }}>Bottom</option>
                                <option value="dress" {{ 'selected' if item.category == 'dress' else '' }}>Dress</option>
                                <option value="outerwear" {{ 'selected' if item.category == 'outerwear' else '' }}>Outerwear</option>
                                <option value="shoes" {{ 'selected' if item.category == 'shoes' else '' }}>Shoes</option>
                                <option value="accessory" {{ 'selected' if item.category == 'accessory' else '' }}>Accessory</option>
                            </select>
                        </div>
                    </div>

                    <!-- Colors -->
                    <div class="mb-4">
                        <label for="colors" class="form-label">
                            <i class="fas fa-palette me-2"></i>Colors *
                        </label>

                        <!-- Color Picker Wheel -->
                        <div class="color-picker-section mb-3">
                            <div class="color-wheel">
                                <div class="color-wheel-label mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Click colors to add them:
                                    </small>
                                </div>
                                <div class="color-grid">
                                    <!-- Basic Colors Row 1 -->
                                    <div class="color-option" data-color="black" style="background-color: #000000" title="Black"></div>
                                    <div class="color-option" data-color="white" style="background-color: #ffffff" title="White"></div>
                                    <div class="color-option" data-color="gray" style="background-color: #808080" title="Gray"></div>
                                    <div class="color-option" data-color="red" style="background-color: #dc3545" title="Red"></div>
                                    <div class="color-option" data-color="blue" style="background-color: #007bff" title="Blue"></div>
                                    <div class="color-option" data-color="green" style="background-color: #28a745" title="Green"></div>
                                    <div class="color-option" data-color="yellow" style="background-color: #ffc107" title="Yellow"></div>
                                    <div class="color-option" data-color="orange" style="background-color: #fd7e14" title="Orange"></div>

                                    <!-- Basic Colors Row 2 -->
                                    <div class="color-option" data-color="purple" style="background-color: #6f42c1" title="Purple"></div>
                                    <div class="color-option" data-color="pink" style="background-color: #e83e8c" title="Pink"></div>
                                    <div class="color-option" data-color="brown" style="background-color: #8b4513" title="Brown"></div>
                                    <div class="color-option" data-color="beige" style="background-color: #f5f5dc" title="Beige"></div>
                                    <div class="color-option" data-color="navy" style="background-color: #000080" title="Navy"></div>
                                    <div class="color-option" data-color="maroon" style="background-color: #800000" title="Maroon"></div>
                                    <div class="color-option" data-color="olive" style="background-color: #808000" title="Olive"></div>
                                    <div class="color-option" data-color="teal" style="background-color: #008080" title="Teal"></div>

                                    <!-- Extended Colors Row 3 -->
                                    <div class="color-option" data-color="cream" style="background-color: #fffdd0" title="Cream"></div>
                                    <div class="color-option" data-color="tan" style="background-color: #d2b48c" title="Tan"></div>
                                    <div class="color-option" data-color="khaki" style="background-color: #f0e68c" title="Khaki"></div>
                                    <div class="color-option" data-color="coral" style="background-color: #ff7f50" title="Coral"></div>
                                    <div class="color-option" data-color="salmon" style="background-color: #fa8072" title="Salmon"></div>
                                    <div class="color-option" data-color="lavender" style="background-color: #e6e6fa" title="Lavender"></div>
                                    <div class="color-option" data-color="mint" style="background-color: #98fb98" title="Mint"></div>
                                    <div class="color-option" data-color="peach" style="background-color: #ffcba4" title="Peach"></div>

                                    <!-- Vibrant Colors Row 4 -->
                                    <div class="color-option" data-color="magenta" style="background-color: #ff00ff" title="Magenta"></div>
                                    <div class="color-option" data-color="cyan" style="background-color: #00ffff" title="Cyan"></div>
                                    <div class="color-option" data-color="lime" style="background-color: #00ff00" title="Lime"></div>
                                    <div class="color-option" data-color="gold" style="background-color: #ffd700" title="Gold"></div>
                                    <div class="color-option" data-color="silver" style="background-color: #c0c0c0" title="Silver"></div>
                                    <div class="color-option" data-color="turquoise" style="background-color: #40e0d0" title="Turquoise"></div>
                                    <div class="color-option" data-color="indigo" style="background-color: #4b0082" title="Indigo"></div>
                                    <div class="color-option" data-color="violet" style="background-color: #8a2be2" title="Violet"></div>

                                    <!-- Pastel Colors Row 5 -->
                                    <div class="color-option" data-color="lightblue" style="background-color: #add8e6" title="Light Blue"></div>
                                    <div class="color-option" data-color="lightgreen" style="background-color: #90ee90" title="Light Green"></div>
                                    <div class="color-option" data-color="lightpink" style="background-color: #ffb6c1" title="Light Pink"></div>
                                    <div class="color-option" data-color="lightyellow" style="background-color: #ffffe0" title="Light Yellow"></div>
                                    <div class="color-option" data-color="lightgray" style="background-color: #d3d3d3" title="Light Gray"></div>
                                    <div class="color-option" data-color="darkblue" style="background-color: #00008b" title="Dark Blue"></div>
                                    <div class="color-option" data-color="darkgreen" style="background-color: #006400" title="Dark Green"></div>
                                    <div class="color-option" data-color="darkred" style="background-color: #8b0000" title="Dark Red"></div>
                                </div>
                            </div>

                            <!-- Selected Colors Display -->
                            <div class="selected-colors mt-3">
                                <div class="selected-colors-label mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Selected colors:
                                    </small>
                                </div>
                                <div class="selected-colors-display" id="selectedColorsDisplay">
                                    <span class="text-muted">No colors selected</span>
                                </div>
                            </div>
                        </div>

                        <!-- Text Input (still available for custom colors) -->
                        <input type="text" class="form-control" id="colors" name="colors" required
                               value="{{ item.colors|join(', ') }}" placeholder="Selected colors will appear here, or type custom colors">
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Colors are automatically added from the picker above, or you can type custom colors separated by commas
                        </div>
                    </div>

                    <!-- Brand -->
                    <div class="mb-3">
                        <label for="brand" class="form-label">Brand</label>
                        <input type="text" class="form-control" id="brand" name="brand"
                               value="{{ item.brand }}" placeholder="e.g., Levi's">
                    </div>

                    <!-- Size and Material -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="size" class="form-label">Size</label>
                            <input type="text" class="form-control" id="size" name="size" 
                                   value="{{ item.size }}" placeholder="e.g., M, 32, 8.5">
                        </div>
                        <div class="col-md-6">
                            <label for="material" class="form-label">Material</label>
                            <input type="text" class="form-control" id="material" name="material" 
                                   value="{{ item.material }}" placeholder="e.g., Cotton, Denim, Leather">
                        </div>
                    </div>

                    <!-- Purchase Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date" 
                                   value="{{ item.purchase_date }}">
                        </div>
                        <div class="col-md-6">
                            <label for="cost" class="form-label">Cost</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="cost" name="cost" 
                                       value="{{ item.cost if item.cost > 0 else '' }}" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <!-- Style Tags -->
                    <div class="mb-3">
                        <label class="form-label">Style Tags</label>
                        <div class="style-tags">
                            {% for style in styles %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="style_{{ style.name }}"
                                       name="styles" value="{{ style.value }}"
                                       {{ 'checked' if style in item.style else '' }}>
                                <label class="form-check-label" for="style_{{ style.name }}">
                                    {{ style.value.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Occasions -->
                    <div class="mb-3">
                        <label class="form-label">Suitable Occasions</label>
                        <div class="occasion-tags">
                            {% for occasion in occasions %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="occasion_{{ occasion.name }}"
                                       name="occasions" value="{{ occasion.value }}"
                                       {{ 'checked' if occasion in item.occasions else '' }}>
                                <label class="form-check-label" for="occasion_{{ occasion.name }}">
                                    {{ occasion.value.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Exclusive Occasions (Optional) -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-lock me-2"></i>Exclusive Occasions (Optional)
                        </label>
                        <div class="form-text mb-2">
                            <strong>Select ONLY if this item should be restricted to specific occasions.</strong><br>
                            If selected, this item will NEVER be paired with items from other occasions.<br>
                            Leave empty for normal occasion flexibility.
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Example:</strong> A formal tuxedo should only be for "Formal" occasions,
                            so it won't be mixed with casual items.
                        </div>
                        <div class="row">
                            {% for occasion in occasions %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="exclusive_occasion_{{ occasion.value }}" name="exclusive_occasions" value="{{ occasion.value }}"
                                           {{ 'checked' if occasion in item.exclusive_occasions else '' }}>
                                    <label class="form-check-label" for="exclusive_occasion_{{ occasion.value }}">
                                        <i class="fas fa-lock me-1"></i>{{ occasion.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label class="form-label">Weather Conditions</label>
                        <div class="weather-tags">
                            {% for weather in weather_options %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="weather_{{ weather.name }}"
                                       name="weather" value="{{ weather.value }}"
                                       {{ 'checked' if weather in item.weather else '' }}>
                                <label class="form-check-label" for="weather_{{ weather.name }}">
                                    {{ weather.value.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Ratings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="formality" class="form-label">Formality Level</label>
                            <div class="rating-container">
                                <input type="range" class="form-range" id="formality" name="formality" 
                                       min="1" max="10" value="{{ item.formality }}" oninput="updateRatingDisplay('formality', this.value)">
                                <div class="rating-labels">
                                    <span>Casual</span>
                                    <span id="formalityValue">{{ item.formality }}</span>
                                    <span>Formal</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="versatility" class="form-label">Versatility</label>
                            <div class="rating-container">
                                <input type="range" class="form-range" id="versatility" name="versatility" 
                                       min="1" max="10" value="{{ item.versatility }}" oninput="updateRatingDisplay('versatility', this.value)">
                                <div class="rating-labels">
                                    <span>Specific</span>
                                    <span id="versatilityValue">{{ item.versatility }}</span>
                                    <span>Versatile</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Condition -->
                    <div class="mb-3">
                        <label for="condition" class="form-label">Condition</label>
                        <select class="form-select" id="condition" name="condition">
                            <option value="excellent" {{ 'selected' if item.condition == 'excellent' else '' }}>Excellent</option>
                            <option value="good" {{ 'selected' if item.condition == 'good' else '' }}>Good</option>
                            <option value="fair" {{ 'selected' if item.condition == 'fair' else '' }}>Fair</option>
                            <option value="poor" {{ 'selected' if item.condition == 'poor' else '' }}>Poor</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Any additional notes about this item...">{{ item.notes }}</textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Item
                        </button>
                        <a href="{{ url_for('view_item', item_id=item.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Color Picker Styles */
.color-picker-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
}

.color-wheel-label {
    text-align: center;
    margin-bottom: 1rem;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.75rem;
    justify-items: center;
    max-width: 450px;
    margin: 0 auto;
}

.color-option {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    z-index: 1;
}

.color-option.selected {
    transform: scale(1.1);
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.color-option[style*="ffffff"]::after {
    color: #333;
    text-shadow: none;
}

.color-option[style*="ffffff"] {
    border-color: #dee2e6;
}

.color-option[style*="000000"] {
    border-color: #6c757d;
}

.selected-colors {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.selected-colors-display {
    min-height: 40px;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.selected-color-tag {
    display: inline-flex;
    align-items: center;
    background: #e9ecef;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
    color: #495057;
    border: 1px solid #ced4da;
}

.selected-color-tag .color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.selected-color-tag .remove-color {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #6c757d;
    font-weight: bold;
}

.selected-color-tag .remove-color:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 0.5rem;
    }

    .color-option {
        width: 32px;
        height: 32px;
    }

    .color-picker-section {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('photoPreview');
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" class="preview-image">
                <p class="text-muted mt-2">Click to change photo</p>
            `;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function updateRatingDisplay(type, value) {
    document.getElementById(type + 'Value').textContent = value;
}

// Color picker functionality
let selectedColors = [];

document.addEventListener('DOMContentLoaded', function() {
    const colorOptions = document.querySelectorAll('.color-option');
    const colorsInput = document.getElementById('colors');
    const selectedColorsDisplay = document.getElementById('selectedColorsDisplay');

    // Initialize with existing colors from the item
    const existingColors = colorsInput.value.split(',').map(c => c.trim().toLowerCase()).filter(c => c);
    selectedColors = [...existingColors];

    // Mark existing colors as selected in the picker
    existingColors.forEach(existingColor => {
        const matchingOption = Array.from(colorOptions).find(option =>
            option.dataset.color.toLowerCase() === existingColor
        );
        if (matchingOption) {
            matchingOption.classList.add('selected');
        }
    });

    // Update display with existing colors
    updateColorsDisplay();

    // Handle color option clicks
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const colorName = this.dataset.color;

            if (this.classList.contains('selected')) {
                // Remove color
                this.classList.remove('selected');
                selectedColors = selectedColors.filter(color => color !== colorName);
            } else {
                // Add color
                this.classList.add('selected');
                if (!selectedColors.includes(colorName)) {
                    selectedColors.push(colorName);
                }
            }

            updateColorsDisplay();
            updateColorsInput();
        });
    });

    // Handle manual input changes
    colorsInput.addEventListener('input', function() {
        const inputColors = this.value.split(',').map(c => c.trim().toLowerCase()).filter(c => c);

        // Reset visual selection
        colorOptions.forEach(option => option.classList.remove('selected'));
        selectedColors = [];

        // Select matching colors in the picker
        inputColors.forEach(inputColor => {
            const matchingOption = Array.from(colorOptions).find(option =>
                option.dataset.color.toLowerCase() === inputColor
            );
            if (matchingOption) {
                matchingOption.classList.add('selected');
                selectedColors.push(matchingOption.dataset.color);
            } else {
                // Add custom color
                selectedColors.push(inputColor);
            }
        });

        updateColorsDisplay();
    });

    function updateColorsDisplay() {
        if (selectedColors.length === 0) {
            selectedColorsDisplay.innerHTML = '<span class="text-muted">No colors selected</span>';
            return;
        }

        const colorMap = {
            'black': '#000000', 'white': '#ffffff', 'gray': '#808080',
            'red': '#dc3545', 'blue': '#007bff', 'green': '#28a745',
            'yellow': '#ffc107', 'orange': '#fd7e14', 'purple': '#6f42c1',
            'pink': '#e83e8c', 'brown': '#8b4513', 'beige': '#f5f5dc',
            'navy': '#000080', 'maroon': '#800000', 'olive': '#808000',
            'teal': '#008080', 'cream': '#fffdd0', 'tan': '#d2b48c',
            'khaki': '#f0e68c', 'coral': '#ff7f50', 'salmon': '#fa8072',
            'lavender': '#e6e6fa', 'mint': '#98fb98', 'peach': '#ffcba4',
            'magenta': '#ff00ff', 'cyan': '#00ffff', 'lime': '#00ff00',
            'gold': '#ffd700', 'silver': '#c0c0c0', 'turquoise': '#40e0d0',
            'indigo': '#4b0082', 'violet': '#8a2be2', 'lightblue': '#add8e6',
            'lightgreen': '#90ee90', 'lightpink': '#ffb6c1', 'lightyellow': '#ffffe0',
            'lightgray': '#d3d3d3', 'darkblue': '#00008b', 'darkgreen': '#006400',
            'darkred': '#8b0000'
        };

        const colorTags = selectedColors.map(color => {
            const colorValue = colorMap[color.toLowerCase()] || '#6c757d';
            return `
                <div class="selected-color-tag">
                    <div class="color-dot" style="background-color: ${colorValue}"></div>
                    ${color.charAt(0).toUpperCase() + color.slice(1)}
                    <span class="remove-color" onclick="removeColor('${color}')">&times;</span>
                </div>
            `;
        }).join('');

        selectedColorsDisplay.innerHTML = colorTags;
    }

    function updateColorsInput() {
        colorsInput.value = selectedColors.join(', ');
    }

    // Make removeColor function global
    window.removeColor = function(colorToRemove) {
        selectedColors = selectedColors.filter(color => color !== colorToRemove);

        // Remove visual selection
        const matchingOption = Array.from(colorOptions).find(option =>
            option.dataset.color === colorToRemove
        );
        if (matchingOption) {
            matchingOption.classList.remove('selected');
        }

        updateColorsDisplay();
        updateColorsInput();
    };
});
</script>
{% endblock %}
