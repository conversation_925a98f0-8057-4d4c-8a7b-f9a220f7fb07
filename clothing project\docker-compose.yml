version: '3.8'

services:
  # Personal Closet Web Application
  personal-closet:
    build: .
    container_name: personal-closet-app
    ports:
      - "5000:5000"
    volumes:
      # Persist uploaded photos and closet data
      - ./data:/app/data
      - ./static/uploads:/app/static/uploads
      # Mount closet data file for persistence
      - ./personal_closet.json:/app/personal_closet.json
    environment:
      - FLASK_ENV=production
      - FLASK_APP=app.py
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/api/closet-stats', timeout=10)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - closet-network

  # Development version with hot reload
  personal-closet-dev:
    build: .
    container_name: personal-closet-dev
    ports:
      - "5001:5000"
    volumes:
      # Mount entire source code for development
      - .:/app
      - ./data:/app/data
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - FLASK_APP=app.py
    command: python -m flask run --host=0.0.0.0 --port=5000 --reload
    restart: unless-stopped
    networks:
      - closet-network
    profiles:
      - dev

networks:
  closet-network:
    driver: bridge

volumes:
  closet-data:
    driver: local
