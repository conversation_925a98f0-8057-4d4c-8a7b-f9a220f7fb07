#!/usr/bin/env python3
"""
VPN Client CLI interface
"""

import click
import signal
import sys
import time
from pathlib import Path
from threading import Event

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from src.client.vpn_client import VPNClient
    from src.utils.logger import setup_logging, get_logger
    from src.utils.config import ConfigManager
except ImportError:
    # Fallback for when running from different directory
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from src.client.vpn_client import VPNClient
    from src.utils.logger import setup_logging, get_logger
    from src.utils.config import ConfigManager

logger = get_logger(__name__)

class ClientCLI:
    """VPN Client CLI management"""
    
    def __init__(self):
        self.client: VPNClient = None
        self.running = False
        self.shutdown_event = Event()
    
    def connect(self, config_path: Path, server_host: str = None, server_port: int = None) -> None:
        """Connect to VPN server"""
        try:
            # Setup logging
            setup_logging(level="INFO", log_file=Path("logs/vpn-client.log"))
            
            # Create client
            self.client = VPNClient(config_path)
            
            # Override server settings if provided
            if server_host:
                self.client.config.server.host = server_host
            if server_port:
                self.client.config.server.port = server_port
            
            # Setup callbacks
            self.client.on_connected = self._on_connected
            self.client.on_disconnected = self._on_disconnected
            
            # Setup signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Connect to server
            host = self.client.config.server.host
            port = self.client.config.server.port
            
            click.echo(f"🔄 Connecting to VPN server {host}:{port}...")
            
            if self.client.connect(host, port):
                self.running = True
                click.echo("Press Ctrl+C to disconnect...")
                
                # Wait for shutdown
                self.shutdown_event.wait()
            else:
                click.echo("❌ Failed to connect to VPN server", err=True)
                sys.exit(1)
                
        except Exception as e:
            click.echo(f"❌ Connection failed: {e}", err=True)
            sys.exit(1)
    
    def disconnect(self) -> None:
        """Disconnect from VPN server"""
        if self.client:
            click.echo("🛑 Disconnecting from VPN server...")
            self.client.disconnect()
            self.running = False
            click.echo("✅ Disconnected from VPN server")
    
    def show_status(self, config_path: Path) -> None:
        """Show client status"""
        try:
            if self.client and self.client.connected:
                status = self.client.get_status()
                
                click.echo("📊 VPN Client Status")
                click.echo("=" * 50)
                click.echo(f"Connected: {'✅ Yes' if status['connected'] else '❌ No'}")
                click.echo(f"Authenticated: {'✅ Yes' if status['authenticated'] else '❌ No'}")
                click.echo(f"Session ID: {status['session_id']}")
                click.echo(f"Assigned IP: {status['assigned_ip'] or 'N/A'}")
                click.echo(f"Server: {status['server_address'] or 'N/A'}")
            else:
                # Show configuration status
                config_manager = ConfigManager(config_path)
                config = config_manager.load_config()
                
                click.echo("📊 VPN Client Configuration")
                click.echo("=" * 50)
                click.echo(f"Configuration: {config_path}")
                click.echo(f"Server Host: {config.server.host}")
                click.echo(f"Server Port: {config.server.port}")
                click.echo(f"Protocol: {config.server.protocol.upper()}")
                click.echo(f"Status: ❌ Not Connected")
                
        except Exception as e:
            click.echo(f"❌ Failed to get status: {e}", err=True)
    
    def test_connection(self, server_host: str, server_port: int) -> None:
        """Test connection to VPN server"""
        try:
            import socket
            
            click.echo(f"🔍 Testing connection to {server_host}:{server_port}...")
            
            # Test TCP connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            result = sock.connect_ex((server_host, server_port))
            sock.close()
            
            if result == 0:
                click.echo("✅ Connection test successful")
            else:
                click.echo("❌ Connection test failed - server unreachable")
                
        except Exception as e:
            click.echo(f"❌ Connection test failed: {e}", err=True)
    
    def generate_client_cert(self, output_dir: Path, client_name: str) -> None:
        """Generate client certificate"""
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            
            click.echo(f"🔐 Generating client certificate for '{client_name}'...")
            
            # This is a simplified certificate generation
            # In a real implementation, you'd use proper OpenSSL commands
            
            client_cert = output_dir / f"{client_name}.crt"
            client_key = output_dir / f"{client_name}.key"
            
            # Create placeholder certificates
            client_cert.write_text(f"# Client Certificate for {client_name}\n")
            client_key.write_text(f"# Client Private Key for {client_name}\n")
            
            click.echo(f"✅ Client certificate generated in {output_dir}")
            click.echo("📝 Note: This is a placeholder certificate.")
            click.echo("📝 For production use, generate proper certificates with OpenSSL.")
            
        except Exception as e:
            click.echo(f"❌ Certificate generation failed: {e}", err=True)
    
    def _on_connected(self) -> None:
        """Callback when connected to server"""
        click.echo("✅ Connected to VPN server")
        if self.client.assigned_ip:
            click.echo(f"🌐 Assigned IP: {self.client.assigned_ip}")
    
    def _on_disconnected(self) -> None:
        """Callback when disconnected from server"""
        click.echo("📡 Disconnected from VPN server")
        self.shutdown_event.set()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        click.echo(f"\n🛑 Received signal {signum}, disconnecting...")
        self.disconnect()
        self.shutdown_event.set()

# CLI Commands
@click.group()
@click.version_option(version="1.0.0")
def cli():
    """VPN Client Management CLI"""
    pass

@cli.command()
@click.option('--config', '-c', type=click.Path(exists=True, path_type=Path), 
              default=Path("config/client.yaml"), help='Configuration file path')
@click.option('--server', '-s', help='Server hostname or IP address')
@click.option('--port', '-p', type=int, help='Server port number')
def connect(config, server, port):
    """Connect to VPN server"""
    client_cli = ClientCLI()
    client_cli.connect(config, server, port)

@cli.command()
def disconnect():
    """Disconnect from VPN server"""
    client_cli = ClientCLI()
    client_cli.disconnect()

@cli.command()
@click.option('--config', '-c', type=click.Path(exists=True, path_type=Path), 
              default=Path("config/client.yaml"), help='Configuration file path')
def status(config):
    """Show client status"""
    client_cli = ClientCLI()
    client_cli.show_status(config)

@cli.command()
@click.argument('server_host')
@click.argument('server_port', type=int)
def test(server_host, server_port):
    """Test connection to VPN server"""
    client_cli = ClientCLI()
    client_cli.test_connection(server_host, server_port)

@cli.command()
@click.option('--output', '-o', type=click.Path(path_type=Path), 
              default=Path("config/certificates"), help='Output directory for certificates')
@click.option('--name', '-n', required=True, help='Client name for certificate')
def generate_cert(output, name):
    """Generate client certificate"""
    client_cli = ClientCLI()
    client_cli.generate_client_cert(output, name)

@cli.command()
@click.option('--config', '-c', type=click.Path(path_type=Path), 
              default=Path("config/client.yaml"), help='Configuration file path')
def init_config(config):
    """Initialize default client configuration"""
    try:
        config.parent.mkdir(parents=True, exist_ok=True)
        
        # Create client-specific config
        config_content = """
client:
  server_host: "127.0.0.1"
  server_port: 1194
  protocol: "udp"

security:
  cert_file: "config/certificates/client.crt"
  key_file: "config/certificates/client.key"
  ca_file: "config/certificates/ca.crt"

logging:
  level: "INFO"
  file: "logs/vpn-client.log"
"""
        
        config.write_text(config_content.strip())
        click.echo(f"✅ Default client configuration created: {config}")
        
    except Exception as e:
        click.echo(f"❌ Configuration initialization failed: {e}", err=True)

def main():
    """Main entry point"""
    cli()

if __name__ == "__main__":
    main()
