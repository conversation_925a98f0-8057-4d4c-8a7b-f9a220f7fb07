@echo off
echo 🚀 Deploying Privacy VPN Server with Docker...
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo ✅ Docker and Docker Compose are available
echo.

REM Create necessary directories
echo 📁 Creating directories...
if not exist logs mkdir logs
if not exist certs mkdir certs
if not exist config mkdir config

REM Build and start services
echo 🔨 Building Docker images...
docker-compose build

if %errorlevel% neq 0 (
    echo ❌ Docker build failed
    pause
    exit /b 1
)

echo 🚀 Starting Privacy VPN services...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    pause
    exit /b 1
)

REM Wait for services to start
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service status
echo 🔍 Checking service status...
docker-compose ps

echo.
echo ✅ Privacy VPN Server deployed successfully!
echo.
echo 📊 Web Management Interface: http://localhost:8080
echo 📈 Monitoring Dashboard: http://localhost:3000 (admin/vpnadmin)
echo 🔌 VPN Server: localhost:1194 (UDP)
echo.
echo 🔐 To connect clients:
echo 1. Generate client certificates:
echo    docker-compose exec privacy-vpn python cli/server_cli.py generate-certs --client
echo.
echo 2. Download client configuration:
echo    docker-compose exec privacy-vpn cat /etc/vpn/client.ovpn ^> client.ovpn
echo.
echo 📋 Useful commands:
echo   View logs: docker-compose logs -f
echo   Stop services: docker-compose down
echo   Restart: docker-compose restart
echo   Update: docker-compose pull ^&^& docker-compose up -d
echo.
echo 🛡️  Privacy Features Enabled:
echo   ✅ DNS-over-HTTPS
echo   ✅ Ad ^& Tracker Blocking
echo   ✅ Traffic Encryption (AES-256)
echo   ✅ No-Logs Policy
echo   ✅ Firewall Protection
echo.

REM Check if web interface is accessible
curl -s http://localhost:8080/health >nul 2>&1
if %errorlevel% equ 0 (
    echo 🌐 Web interface is accessible
) else (
    echo ⚠️  Web interface may not be ready yet. Please wait a moment and try again.
)

echo.
echo 🎉 Deployment complete! Your privacy VPN is ready to use.
echo.
echo 🔒 IMPORTANT PRIVACY NOTES:
echo • Change default passwords before production use
echo • Generate strong certificates for your domain
echo • Regularly update blocklists and security rules
echo • Test your privacy with: python test-privacy.py
echo.
pause
