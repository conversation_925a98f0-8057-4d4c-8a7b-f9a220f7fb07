"""
VPN Client implementation
"""

import socket
import threading
import time
import random
from typing import Op<PERSON>, <PERSON><PERSON>, Callable
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import Config<PERSON><PERSON><PERSON>, VPNConfig
from ..utils.protocol import (
    VPNPacket, PacketType, ProtocolHandler, 
    HandshakeData, ConfigData
)
from .connection_manager import ConnectionManager

logger = get_logger(__name__)

class VPNClient:
    """VPN Client for connecting to VPN server"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_manager = ConfigManager(config_path or Path("config/client.yaml"))
        self.config: VPNConfig = self.config_manager.load_config()
        
        self.protocol_handler = ProtocolHandler()
        self.connection_manager = ConnectionManager(self.config)
        
        self.socket: Optional[socket.socket] = None
        self.session_id = random.randint(1000, 99999)
        self.connected = False
        self.authenticated = False
        self.running = False
        
        self.server_address: Optional[Tuple[str, int]] = None
        self.assigned_ip: Optional[str] = None
        self.client_thread: Optional[threading.Thread] = None
        
        # Callbacks
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_data_received: Optional[Callable[[bytes], None]] = None
        
        logger.info("VPN Client initialized", session_id=self.session_id)
    
    def connect(self, server_host: str, server_port: int) -> bool:
        """Connect to VPN server"""
        if self.connected:
            logger.warning("Client is already connected")
            return True
        
        try:
            self.server_address = (server_host, server_port)
            
            # Create socket
            if self.config.server.protocol.lower() == 'udp':
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            else:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.connect(self.server_address)
            
            # Start client thread
            self.running = True
            self.client_thread = threading.Thread(target=self._client_loop, daemon=True)
            self.client_thread.start()
            
            # Initiate handshake
            if self._perform_handshake():
                logger.info("Connected to VPN server", server=f"{server_host}:{server_port}")
                self.connected = True
                
                if self.on_connected:
                    self.on_connected()
                
                return True
            else:
                logger.error("Handshake failed")
                self.disconnect()
                return False
                
        except Exception as e:
            logger.error("Connection failed", error=str(e))
            self.disconnect()
            return False
    
    def disconnect(self) -> None:
        """Disconnect from VPN server"""
        if not self.connected:
            return
        
        logger.info("Disconnecting from VPN server")
        
        try:
            # Send disconnect packet
            if self.socket and self.server_address:
                disconnect_packet = self.protocol_handler.create_packet(
                    PacketType.DISCONNECT,
                    self.session_id,
                    b"CLIENT_DISCONNECT"
                )
                self._send_packet(disconnect_packet)
        except:
            pass
        
        # Stop client thread
        self.running = False
        self.connected = False
        self.authenticated = False
        
        # Close socket
        if self.socket:
            self.socket.close()
            self.socket = None
        
        # Cleanup network
        self.connection_manager.cleanup()
        
        # Wait for client thread
        if self.client_thread and self.client_thread.is_alive():
            self.client_thread.join(timeout=5)
        
        if self.on_disconnected:
            self.on_disconnected()
        
        logger.info("Disconnected from VPN server")
    
    def send_data(self, data: bytes) -> bool:
        """Send data through VPN tunnel"""
        if not self.authenticated:
            logger.warning("Cannot send data - not authenticated")
            return False
        
        try:
            data_packet = self.protocol_handler.create_data_packet(self.session_id, data)
            return self._send_packet(data_packet)
        except Exception as e:
            logger.error("Failed to send data", error=str(e))
            return False
    
    def _perform_handshake(self) -> bool:
        """Perform handshake with server"""
        try:
            # Create handshake data
            handshake_data = HandshakeData(
                client_version="1.0.0",
                supported_ciphers=["AES-256-GCM", "AES-128-GCM"],
                client_cert=b"CLIENT_CERT_PLACEHOLDER"  # TODO: Load actual certificate
            )
            
            # Send handshake initiation
            handshake_packet = self.protocol_handler.create_handshake_init(
                self.session_id, handshake_data
            )
            
            if not self._send_packet(handshake_packet):
                return False
            
            # Wait for handshake response
            response = self._wait_for_packet(PacketType.HANDSHAKE_RESPONSE, timeout=10)
            if not response:
                logger.error("No handshake response received")
                return False
            
            logger.info("Handshake completed")
            
            # Perform authentication
            return self._perform_authentication()
            
        except Exception as e:
            logger.error("Handshake failed", error=str(e))
            return False
    
    def _perform_authentication(self) -> bool:
        """Perform authentication with server"""
        try:
            # Send authentication request
            auth_packet = self.protocol_handler.create_packet(
                PacketType.AUTH_REQUEST,
                self.session_id,
                b"AUTH_REQUEST_PLACEHOLDER"  # TODO: Implement proper auth
            )
            
            if not self._send_packet(auth_packet):
                return False
            
            # Wait for configuration
            config_response = self._wait_for_packet(PacketType.CONFIG_PUSH, timeout=10)
            if not config_response:
                logger.error("No configuration received")
                return False
            
            # Parse configuration
            config_data = ConfigData.from_bytes(config_response.payload)
            if not config_data:
                logger.error("Invalid configuration data")
                return False
            
            # Apply configuration
            self.assigned_ip = config_data.assigned_ip
            if self.connection_manager.configure_network(config_data):
                self.authenticated = True
                logger.info("Authentication successful", assigned_ip=self.assigned_ip)
                
                # Send configuration acknowledgment
                ack_packet = self.protocol_handler.create_packet(
                    PacketType.CONFIG_ACK,
                    self.session_id,
                    b"CONFIG_OK"
                )
                self._send_packet(ack_packet)
                
                return True
            else:
                logger.error("Network configuration failed")
                return False
                
        except Exception as e:
            logger.error("Authentication failed", error=str(e))
            return False
    
    def _client_loop(self) -> None:
        """Main client loop"""
        logger.info("Client loop started")
        
        try:
            if self.config.server.protocol.lower() == 'udp':
                self._udp_client_loop()
            else:
                self._tcp_client_loop()
        except Exception as e:
            logger.error("Client loop error", error=str(e))
        finally:
            logger.info("Client loop ended")
    
    def _udp_client_loop(self) -> None:
        """UDP client loop"""
        self.socket.settimeout(1.0)
        last_keepalive = time.time()
        
        while self.running:
            try:
                # Send keepalive if needed
                if time.time() - last_keepalive > 30:
                    if self.authenticated:
                        keepalive = self.protocol_handler.create_keepalive(self.session_id)
                        self._send_packet(keepalive)
                    last_keepalive = time.time()
                
                # Receive data
                try:
                    data, addr = self.socket.recvfrom(4096)
                    if data and addr == self.server_address:
                        self._handle_packet(data)
                except socket.timeout:
                    continue
                    
            except Exception as e:
                if self.running:
                    logger.error("UDP client loop error", error=str(e))
    
    def _tcp_client_loop(self) -> None:
        """TCP client loop"""
        self.socket.settimeout(1.0)
        last_keepalive = time.time()
        
        while self.running:
            try:
                # Send keepalive if needed
                if time.time() - last_keepalive > 30:
                    if self.authenticated:
                        keepalive = self.protocol_handler.create_keepalive(self.session_id)
                        self._send_packet(keepalive)
                    last_keepalive = time.time()
                
                # Receive data
                try:
                    data = self.socket.recv(4096)
                    if data:
                        self._handle_packet(data)
                    else:
                        # Connection closed
                        break
                except socket.timeout:
                    continue
                    
            except Exception as e:
                if self.running:
                    logger.error("TCP client loop error", error=str(e))
                break
    
    def _handle_packet(self, data: bytes) -> None:
        """Handle incoming packet"""
        packet = VPNPacket.from_bytes(data)
        if not packet:
            logger.warning("Invalid packet received")
            return
        
        logger.debug("Packet received", type=packet.packet_type.name)
        
        try:
            if packet.packet_type == PacketType.DATA:
                self._handle_data_packet(packet)
            elif packet.packet_type == PacketType.KEEPALIVE:
                logger.debug("Keepalive received")
            elif packet.packet_type == PacketType.DISCONNECT:
                logger.info("Server requested disconnect")
                self.disconnect()
            else:
                # Store packet for waiting threads
                self._store_received_packet(packet)
                
        except Exception as e:
            logger.error("Packet handling error", error=str(e))
    
    def _handle_data_packet(self, packet: VPNPacket) -> None:
        """Handle data packet"""
        if self.on_data_received:
            self.on_data_received(packet.payload)
        
        # Route packet to local network
        self.connection_manager.route_to_local(packet.payload)
    
    def _send_packet(self, packet: VPNPacket) -> bool:
        """Send packet to server"""
        try:
            data = packet.to_bytes()
            if self.config.server.protocol.lower() == 'udp':
                self.socket.sendto(data, self.server_address)
            else:
                self.socket.send(data)
            return True
        except Exception as e:
            logger.error("Failed to send packet", error=str(e))
            return False
    
    def _wait_for_packet(self, packet_type: PacketType, timeout: int = 10) -> Optional[VPNPacket]:
        """Wait for specific packet type"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check stored packets
            packet = self._get_stored_packet(packet_type)
            if packet:
                return packet
            
            time.sleep(0.1)
        
        return None
    
    def _store_received_packet(self, packet: VPNPacket) -> None:
        """Store received packet for waiting threads"""
        # Simple implementation - in production, use proper queue
        if not hasattr(self, '_stored_packets'):
            self._stored_packets = []
        
        self._stored_packets.append(packet)
        
        # Keep only recent packets
        if len(self._stored_packets) > 100:
            self._stored_packets = self._stored_packets[-50:]
    
    def _get_stored_packet(self, packet_type: PacketType) -> Optional[VPNPacket]:
        """Get stored packet of specific type"""
        if not hasattr(self, '_stored_packets'):
            return None
        
        for i, packet in enumerate(self._stored_packets):
            if packet.packet_type == packet_type:
                return self._stored_packets.pop(i)
        
        return None
    
    def get_status(self) -> dict:
        """Get client status"""
        return {
            'connected': self.connected,
            'authenticated': self.authenticated,
            'session_id': self.session_id,
            'assigned_ip': self.assigned_ip,
            'server_address': f"{self.server_address[0]}:{self.server_address[1]}" if self.server_address else None,
        }
