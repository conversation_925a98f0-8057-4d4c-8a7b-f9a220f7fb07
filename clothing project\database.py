"""
Database module for persistent storage of clothing items and outfits.
Uses SQLite for reliable data persistence.
"""

import sqlite3
import json
import os
from typing import Dict, List, Optional, Any, TYPE_CHECKING
from datetime import datetime
from contextlib import contextmanager
from clothing_generator import Style, Occasion, Weather

if TYPE_CHECKING:
    from personal_closet import PersonalClothingItem, PersonalOutfit


class ClothingDatabase:
    """SQLite database manager for clothing items and outfits"""
    
    def __init__(self, db_path: str = "data/closet.db"):
        self.db_path = db_path
        # Ensure data directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Clothing items table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS clothing_items (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    colors TEXT NOT NULL,  -- JSON array
                    brand TEXT DEFAULT '',
                    size TEXT DEFAULT '',
                    material TEXT DEFAULT '',
                    purchase_date TEXT DEFAULT '',
                    cost REAL DEFAULT 0.0,
                    photo_filename TEXT,
                    condition TEXT DEFAULT 'good',
                    last_worn TEXT,
                    wear_count INTEGER DEFAULT 0,
                    notes TEXT DEFAULT '',
                    date_added TEXT NOT NULL,
                    is_favorite BOOLEAN DEFAULT 0,
                    style TEXT NOT NULL,  -- JSON array
                    occasions TEXT NOT NULL,  -- JSON array
                    weather TEXT NOT NULL,  -- JSON array
                    formality INTEGER DEFAULT 5,
                    versatility INTEGER DEFAULT 5,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Outfits table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS outfits (
                    id TEXT PRIMARY KEY,
                    occasion TEXT NOT NULL,
                    weather TEXT NOT NULL,
                    date_created TEXT NOT NULL,
                    date_worn TEXT,
                    rating INTEGER,
                    notes TEXT DEFAULT '',
                    is_favorite BOOLEAN DEFAULT 0,
                    style_score REAL DEFAULT 0.0,
                    color_harmony REAL DEFAULT 0.0,
                    occasion_fit REAL DEFAULT 0.0,
                    overall_score REAL DEFAULT 0.0,
                    description TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Outfit items junction table (many-to-many relationship)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS outfit_items (
                    outfit_id TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    PRIMARY KEY (outfit_id, item_id),
                    FOREIGN KEY (outfit_id) REFERENCES outfits (id) ON DELETE CASCADE,
                    FOREIGN KEY (item_id) REFERENCES clothing_items (id) ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_items_category ON clothing_items (category)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_items_favorite ON clothing_items (is_favorite)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_outfits_favorite ON outfits (is_favorite)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_outfits_date ON outfits (date_created)")
            
            conn.commit()
    
    def save_item(self, item: 'PersonalClothingItem') -> bool:
        """Save or update a clothing item"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Convert lists and enums to JSON strings
                colors_json = json.dumps(item.colors)
                style_json = json.dumps([s.value for s in item.style])
                occasions_json = json.dumps([o.value for o in item.occasions])
                weather_json = json.dumps([w.value for w in item.weather])
                
                cursor.execute("""
                    INSERT OR REPLACE INTO clothing_items (
                        id, name, category, colors, brand, size, material, purchase_date,
                        cost, photo_filename, condition, last_worn, wear_count, notes,
                        date_added, is_favorite, style, occasions, weather, formality,
                        versatility, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    item.id, item.name, item.category, colors_json, item.brand, item.size,
                    item.material, item.purchase_date, item.cost, item.photo_filename,
                    item.condition, item.last_worn, item.wear_count, item.notes,
                    item.date_added, item.is_favorite, style_json, occasions_json,
                    weather_json, item.formality, item.versatility, datetime.now().isoformat()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error saving item: {e}")
            return False
    
    def get_item(self, item_id: str) -> Optional['PersonalClothingItem']:
        """Get a clothing item by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM clothing_items WHERE id = ?", (item_id,))
                row = cursor.fetchone()

                if row:
                    return self._row_to_item(row)
                return None

        except Exception as e:
            print(f"Error getting item: {e}")
            return None

    def get_all_items(self) -> List['PersonalClothingItem']:
        """Get all clothing items"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM clothing_items ORDER BY date_added DESC")
                rows = cursor.fetchall()

                return [self._row_to_item(row) for row in rows]

        except Exception as e:
            print(f"Error getting all items: {e}")
            return []
    
    def delete_item(self, item_id: str) -> bool:
        """Delete a clothing item"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM clothing_items WHERE id = ?", (item_id,))
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"Error deleting item: {e}")
            return False
    
    def get_favorite_items(self) -> List['PersonalClothingItem']:
        """Get all favorite clothing items"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM clothing_items WHERE is_favorite = 1 ORDER BY date_added DESC")
                rows = cursor.fetchall()

                return [self._row_to_item(row) for row in rows]

        except Exception as e:
            print(f"Error getting favorite items: {e}")
            return []

    def save_outfit(self, outfit: 'PersonalOutfit') -> bool:
        """Save or update an outfit"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Save outfit data
                cursor.execute("""
                    INSERT OR REPLACE INTO outfits (
                        id, occasion, weather, date_created, date_worn, rating, notes,
                        is_favorite, style_score, color_harmony, occasion_fit, overall_score,
                        description, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    outfit.id, outfit.occasion.value, outfit.weather.value, outfit.date_created,
                    outfit.date_worn, outfit.rating, outfit.notes, outfit.is_favorite,
                    outfit.style_score, outfit.color_harmony, outfit.occasion_fit,
                    outfit.overall_score, outfit.description, datetime.now().isoformat()
                ))
                
                # Delete existing outfit items
                cursor.execute("DELETE FROM outfit_items WHERE outfit_id = ?", (outfit.id,))
                
                # Save outfit items
                for item in outfit.items:
                    cursor.execute("""
                        INSERT INTO outfit_items (outfit_id, item_id) VALUES (?, ?)
                    """, (outfit.id, item.id))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error saving outfit: {e}")
            return False
    
    def get_all_outfits(self) -> List['PersonalOutfit']:
        """Get all outfits with their items"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM outfits ORDER BY date_created DESC")
                outfit_rows = cursor.fetchall()

                outfits = []
                for outfit_row in outfit_rows:
                    # Get items for this outfit
                    cursor.execute("""
                        SELECT ci.* FROM clothing_items ci
                        JOIN outfit_items oi ON ci.id = oi.item_id
                        WHERE oi.outfit_id = ?
                    """, (outfit_row['id'],))
                    item_rows = cursor.fetchall()

                    items = [self._row_to_item(row) for row in item_rows]
                    outfit = self._row_to_outfit(outfit_row, items)
                    outfits.append(outfit)

                return outfits

        except Exception as e:
            print(f"Error getting all outfits: {e}")
            return []

    def get_favorite_outfits(self) -> List['PersonalOutfit']:
        """Get all favorite outfits"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM outfits WHERE is_favorite = 1 ORDER BY date_created DESC")
                outfit_rows = cursor.fetchall()
                
                outfits = []
                for outfit_row in outfit_rows:
                    # Get items for this outfit
                    cursor.execute("""
                        SELECT ci.* FROM clothing_items ci
                        JOIN outfit_items oi ON ci.id = oi.item_id
                        WHERE oi.outfit_id = ?
                    """, (outfit_row['id'],))
                    item_rows = cursor.fetchall()
                    
                    items = [self._row_to_item(row) for row in item_rows]
                    outfit = self._row_to_outfit(outfit_row, items)
                    outfits.append(outfit)
                
                return outfits
                
        except Exception as e:
            print(f"Error getting favorite outfits: {e}")
            return []
    
    def _row_to_item(self, row) -> 'PersonalClothingItem':
        """Convert database row to PersonalClothingItem"""
        # Import here to avoid circular import
        from personal_closet import PersonalClothingItem

        colors = json.loads(row['colors'])
        style = [Style(s) for s in json.loads(row['style'])]
        occasions = [Occasion(o) for o in json.loads(row['occasions'])]
        weather = [Weather(w) for w in json.loads(row['weather'])]

        return PersonalClothingItem(
            id=row['id'],
            name=row['name'],
            category=row['category'],
            colors=colors,
            style=style,
            occasions=occasions,
            weather=weather,
            formality=row['formality'],
            versatility=row['versatility'],
            brand=row['brand'] or '',
            size=row['size'] or '',
            material=row['material'] or '',
            purchase_date=row['purchase_date'] or '',
            cost=row['cost'] or 0.0,
            photo_filename=row['photo_filename'],
            condition=row['condition'] or 'good',
            last_worn=row['last_worn'],
            wear_count=row['wear_count'] or 0,
            notes=row['notes'] or '',
            date_added=row['date_added'],
            is_favorite=bool(row['is_favorite'])
        )
    
    def _row_to_outfit(self, row, items: List['PersonalClothingItem']) -> 'PersonalOutfit':
        """Convert database row to PersonalOutfit"""
        # Import here to avoid circular import
        from personal_closet import PersonalOutfit

        return PersonalOutfit(
            id=row['id'],
            items=items,
            occasion=Occasion(row['occasion']),
            weather=Weather(row['weather']),
            date_created=row['date_created'],
            date_worn=row['date_worn'],
            rating=row['rating'],
            notes=row['notes'] or '',
            is_favorite=bool(row['is_favorite']),
            style_score=row['style_score'] or 0.0,
            color_harmony=row['color_harmony'] or 0.0,
            occasion_fit=row['occasion_fit'] or 0.0,
            overall_score=row['overall_score'] or 0.0,
            description=row['description'] or ''
        )
