{% extends "base.html" %}

{% block title %}Favorites - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-heart me-3"></i>Favorites</h1>
    <p>Your favorite outfits and clothing items</p>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-4" id="favoriteTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="outfits-tab" data-bs-toggle="tab" data-bs-target="#outfits" 
                type="button" role="tab" aria-controls="outfits" aria-selected="true">
            <i class="fas fa-tshirt me-2"></i>Favorite Outfits
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" 
                type="button" role="tab" aria-controls="items" aria-selected="false">
            <i class="fas fa-star me-2"></i>Favorite Items
        </button>
    </li>
</ul>

<div class="tab-content" id="favoriteTabContent">
    <!-- Favorite Outfits Tab -->
    <div class="tab-pane fade show active" id="outfits" role="tabpanel" aria-labelledby="outfits-tab">
        {% if favorites %}
        <div class="row">
            {% for outfit in favorites %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card outfit-card h-100">
                    <div class="card-body">
                        <!-- Outfit Header -->
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="card-title">{{ outfit.occasion.name.replace('_', ' ').title() }} Outfit</h5>
                                <small class="text-muted">{{ outfit.date_created[:10] }}</small>
                            </div>
                            <button class="favorite-btn active" onclick="toggleFavorite('{{ outfit.id }}', this)">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>

                        <!-- Outfit Items -->
                        <div class="outfit-items mb-3">
                            {% for item in outfit.items %}
                            <div class="outfit-item-mini">
                                {% if item.photo_filename %}
                                <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                     alt="{{ item.name }}" class="outfit-item-image">
                                {% else %}
                                <div class="outfit-item-placeholder">
                                    <i class="fas fa-tshirt"></i>
                                </div>
                                {% endif %}
                                <small>{{ item.name }}</small>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Outfit Stats -->
                        <div class="outfit-stats mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-value">{{ "%.1f"|format(outfit.overall_score) }}</div>
                                    <div class="stat-label">Overall</div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-value">{{ "%.1f"|format(outfit.style_score) }}</div>
                                    <div class="stat-label">Style</div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-value">{{ "%.1f"|format(outfit.color_harmony) }}</div>
                                    <div class="stat-label">Harmony</div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        {% if outfit.description %}
                        <p class="card-text text-muted small">{{ outfit.description }}</p>
                        {% endif %}

                        <!-- Weather and Occasion Tags -->
                        <div class="mb-3">
                            <span class="badge bg-primary me-1">{{ outfit.weather.name.replace('_', ' ').title() }}</span>
                            <span class="badge bg-secondary">{{ outfit.occasion.name.replace('_', ' ').title() }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- Empty State for Outfits -->
        <div class="text-center py-5">
            <i class="fas fa-heart fa-4x text-muted mb-4"></i>
            <h4>No Favorite Outfits</h4>
            <p class="text-muted mb-4">You haven't favorited any outfits yet. Generate some outfits and mark your favorites!</p>
            <a href="{{ url_for('generate_outfit') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-magic me-2"></i>Generate Outfits
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Favorite Items Tab -->
    <div class="tab-pane fade" id="items" role="tabpanel" aria-labelledby="items-tab">
        <div id="favoriteItemsContainer">
            <!-- This will be loaded via JavaScript -->
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-muted mt-3">Loading favorite items...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.outfit-card {
    transition: transform 0.2s;
}

.outfit-card:hover {
    transform: translateY(-2px);
}

.outfit-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.outfit-item-mini {
    text-align: center;
    flex: 1;
    min-width: 60px;
}

.outfit-item-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.outfit-item-placeholder {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.outfit-item-mini small {
    display: block;
    margin-top: 4px;
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.outfit-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #dee2e6;
    cursor: pointer;
    transition: color 0.2s;
}

.favorite-btn:hover {
    color: #dc3545;
}

.favorite-btn.active {
    color: #dc3545;
}

.clothing-item-card {
    transition: transform 0.2s;
}

.clothing-item-card:hover {
    transform: translateY(-2px);
}

.clothing-item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.clothing-item-placeholder {
    width: 100%;
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Load favorite items when the tab is clicked
document.getElementById('items-tab').addEventListener('click', function() {
    loadFavoriteItems();
});

function loadFavoriteItems() {
    fetch('/api/favorite-items')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('favoriteItemsContainer');
            
            if (data.items && data.items.length > 0) {
                let html = '<div class="row">';
                data.items.forEach(item => {
                    html += `
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card clothing-item-card h-100">
                                <div class="position-relative">
                                    ${item.photo_filename ? 
                                        `<img src="/static/uploads/${item.photo_filename}" class="clothing-item-image" alt="${item.name}">` :
                                        `<div class="clothing-item-placeholder"><i class="fas fa-tshirt"></i></div>`
                                    }
                                    <button class="favorite-btn active position-absolute top-0 end-0 m-2" 
                                            onclick="toggleItemFavorite('${item.id}', this)">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">${item.name}</h6>
                                    <p class="card-text text-muted small">${item.brand || 'No brand'}</p>
                                    <div class="d-flex gap-2">
                                        <a href="/item/${item.id}" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="/edit-item/${item.id}" class="btn btn-outline-secondary btn-sm flex-fill">
                                            <i class="fas fa-edit me-1"></i>Edit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-star fa-4x text-muted mb-4"></i>
                        <h4>No Favorite Items</h4>
                        <p class="text-muted mb-4">You haven't favorited any clothing items yet. Mark some items as favorites!</p>
                        <a href="/closet" class="btn btn-primary btn-lg">
                            <i class="fas fa-tshirt me-2"></i>View Your Closet
                        </a>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading favorite items:', error);
            document.getElementById('favoriteItemsContainer').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
                    <h4>Error Loading Items</h4>
                    <p class="text-muted">There was an error loading your favorite items. Please try again.</p>
                </div>
            `;
        });
}

function toggleItemFavorite(itemId, element) {
    fetch(`/toggle-item-favorite/${itemId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (!data.is_favorite) {
                // Item was unfavorited, remove it from the view
                element.closest('.col-lg-3').remove();
                
                // Check if there are no more items
                const container = document.getElementById('favoriteItemsContainer');
                const itemCards = container.querySelectorAll('.col-lg-3');
                if (itemCards.length === 0) {
                    loadFavoriteItems(); // Reload to show empty state
                }
            }
        }
    })
    .catch(error => console.error('Error:', error));
}
</script>
{% endblock %}
