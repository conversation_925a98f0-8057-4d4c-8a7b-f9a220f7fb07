{% extends "base.html" %}

{% block title %}Outfit History - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-history me-3"></i>Outfit History</h1>
    <p>View all your previously generated outfits</p>
</div>

{% if outfits %}
<div class="row">
    {% for outfit in outfits %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card outfit-card h-100">
            <div class="card-body">
                <!-- Outfit Header -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="card-title">{{ outfit.description or (outfit.occasion.name.replace('_', ' ').title() + ' Outfit') }}</h5>
                        <small class="text-muted">{{ outfit.date_created[:10] }}</small>
                    </div>
                    <div class="outfit-actions">
                        <button class="favorite-btn {{ 'active' if outfit.is_favorite else '' }}"
                                onclick="toggleFavorite('{{ outfit.id }}', this)"
                                data-bs-toggle="tooltip" title="Toggle Favorite">
                            <i class="fa{{ 's' if outfit.is_favorite else 'r' }} fa-heart"></i>
                        </button>
                        <button class="delete-btn"
                                onclick="deleteOutfit('{{ outfit.id }}', this)"
                                data-bs-toggle="tooltip" title="Delete Outfit">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- Outfit Items -->
                <div class="outfit-items mb-3">
                    {% for item in outfit.items %}
                    <div class="outfit-item-mini">
                        {% if item.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                             alt="{{ item.name }}" class="outfit-item-image">
                        {% else %}
                        <div class="outfit-item-placeholder">
                            <i class="fas fa-tshirt"></i>
                        </div>
                        {% endif %}
                        <small>{{ item.name }}</small>
                    </div>
                    {% endfor %}
                </div>

                <!-- Outfit Stats -->
                <div class="outfit-stats mb-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.overall_score) }}</div>
                            <div class="stat-label">Overall</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.style_score) }}</div>
                            <div class="stat-label">Style</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.color_harmony) }}</div>
                            <div class="stat-label">Harmony</div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                {% if outfit.description %}
                <p class="card-text text-muted small">{{ outfit.description }}</p>
                {% endif %}

                <!-- Color Palette -->
                <div class="color-palette mb-3">
                    <div class="color-palette-label">
                        <i class="fas fa-palette me-1"></i>
                        <small>Colors</small>
                    </div>
                    <div class="color-circles">
                        {% set outfit_colors = [] %}
                        {% for item in outfit.items %}
                            {% if item.colors %}
                                {% for color in item.colors %}
                                    {% if color.lower() not in outfit_colors %}
                                        {% set _ = outfit_colors.append(color.lower()) %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                        {% endfor %}

                        {% set color_map = {
                            'black': '#000000', 'white': '#ffffff', 'gray': '#808080', 'grey': '#808080',
                            'red': '#dc3545', 'blue': '#007bff', 'green': '#28a745', 'yellow': '#ffc107',
                            'orange': '#fd7e14', 'purple': '#6f42c1', 'pink': '#e83e8c', 'brown': '#8b4513',
                            'beige': '#f5f5dc', 'navy': '#000080', 'maroon': '#800000', 'olive': '#808000',
                            'cream': '#fffdd0', 'tan': '#d2b48c', 'khaki': '#f0e68c', 'coral': '#ff7f50',
                            'salmon': '#fa8072', 'lavender': '#e6e6fa', 'mint': '#98fb98', 'peach': '#ffcba4',
                            'magenta': '#ff00ff', 'cyan': '#00ffff', 'lime': '#00ff00', 'gold': '#ffd700',
                            'silver': '#c0c0c0', 'turquoise': '#40e0d0', 'indigo': '#4b0082', 'violet': '#8a2be2',
                            'lightblue': '#add8e6', 'lightgreen': '#90ee90', 'lightpink': '#ffb6c1',
                            'lightyellow': '#ffffe0', 'lightgray': '#d3d3d3', 'darkblue': '#00008b',
                            'darkgreen': '#006400', 'darkred': '#8b0000', 'teal': '#008080'
                        } %}

                        {% for color in outfit_colors[:5] %}
                            {% set hex_color = color_map.get(color, '#6c757d') %}
                            <div class="color-circle" style="background-color: {{ hex_color }}"
                                 title="{{ color.title() }}"></div>
                        {% endfor %}

                        {% if outfit_colors|length == 0 %}
                            <div class="color-circle" style="background-color: #6c757d" title="Neutral"></div>
                        {% endif %}
                    </div>
                </div>

                <!-- Weather and Occasion Tags -->
                <div class="mb-3">
                    <span class="badge bg-primary me-1">{{ outfit.weather.name.replace('_', ' ').title() }}</span>
                    <span class="badge bg-secondary">{{ outfit.occasion.name.replace('_', ' ').title() }}</span>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    <i class="fas fa-history fa-4x text-muted mb-4"></i>
    <h4>No Outfit History</h4>
    <p class="text-muted mb-4">You haven't generated any outfits yet. Start creating some outfits to see them here!</p>
    <a href="{{ url_for('generate_outfit') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-magic me-2"></i>Generate Your First Outfit
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.outfit-card {
    transition: transform 0.2s;
}

.outfit-card:hover {
    transform: translateY(-2px);
}

.outfit-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.outfit-item-mini {
    text-align: center;
    flex: 1;
    min-width: 60px;
}

.outfit-item-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.outfit-item-placeholder {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.outfit-item-mini small {
    display: block;
    margin-top: 4px;
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.outfit-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #dee2e6;
    cursor: pointer;
    transition: color 0.2s;
}

.favorite-btn:hover {
    color: #dc3545;
}

.favorite-btn.active {
    color: #dc3545;
}

.outfit-actions {
    display: flex;
    gap: 8px;
}

.delete-btn {
    background: none;
    border: none;
    font-size: 1.1rem;
    color: #dee2e6;
    cursor: pointer;
    transition: color 0.2s;
    padding: 4px;
}

.delete-btn:hover {
    color: #dc3545;
}

.outfit-card.deleting {
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.3s;
}

/* Color Palette Styles */
.color-palette {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.color-palette-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
    font-weight: 500;
}

.color-circles {
    display: flex;
    gap: 0.4rem;
    flex-wrap: wrap;
    justify-content: center;
}

.color-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
}

.color-circle:hover {
    transform: scale(1.3);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.color-circle[style*="ffffff"] {
    border-color: #dee2e6;
}

.color-circle[style*="000000"] {
    border-color: #6c757d;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function deleteOutfit(outfitId, button) {
    // Get the outfit card element
    const outfitCard = button.closest('.outfit-card');
    const outfitTitle = outfitCard.querySelector('.card-title').textContent;

    // Confirm deletion
    if (confirm(`Are you sure you want to delete "${outfitTitle}"?\n\nThis action cannot be undone.`)) {
        // Add deleting class for visual feedback
        outfitCard.classList.add('deleting');

        // Send delete request
        fetch(`/delete-outfit/${outfitId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the outfit card with animation
                outfitCard.style.transition = 'all 0.3s ease';
                outfitCard.style.transform = 'scale(0)';
                outfitCard.style.opacity = '0';

                setTimeout(() => {
                    outfitCard.remove();

                    // Check if there are any outfits left
                    const remainingOutfits = document.querySelectorAll('.outfit-card').length;
                    if (remainingOutfits === 0) {
                        // Show empty state
                        location.reload();
                    }
                }, 300);

                showAlert('Outfit deleted successfully!', 'success');
            } else {
                // Remove deleting class on error
                outfitCard.classList.remove('deleting');
                showAlert('Error deleting outfit: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            outfitCard.classList.remove('deleting');
            showAlert('Error deleting outfit', 'error');
        });
    }
}

function toggleFavorite(outfitId, button) {
    const icon = button.querySelector('i');
    const isCurrentlyFavorite = button.classList.contains('active');

    // Send request to toggle favorite
    fetch(`/toggle-outfit-favorite/${outfitId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Toggle visual state
            if (isCurrentlyFavorite) {
                button.classList.remove('active');
                icon.classList.remove('fas');
                icon.classList.add('far');
            } else {
                button.classList.add('active');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }

            showAlert(data.is_favorite ? 'Added to favorites!' : 'Removed from favorites!', 'success');
        } else {
            showAlert('Error updating favorite status: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating favorite status', 'error');
    });
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
