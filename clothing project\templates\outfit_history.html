{% extends "base.html" %}

{% block title %}Outfit History - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-history me-3"></i>Outfit History</h1>
    <p>View all your previously generated outfits</p>
</div>

{% if outfits %}
<div class="row">
    {% for outfit in outfits %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card outfit-card h-100">
            <div class="card-body">
                <!-- Outfit Header -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="card-title">{{ outfit.occasion.name.replace('_', ' ').title() }} Outfit</h5>
                        <small class="text-muted">{{ outfit.date_created[:10] }}</small>
                    </div>
                    <button class="favorite-btn {{ 'active' if outfit.is_favorite else '' }}" 
                            onclick="toggleFavorite('{{ outfit.id }}', this)">
                        <i class="fa{{ 's' if outfit.is_favorite else 'r' }} fa-heart"></i>
                    </button>
                </div>

                <!-- Outfit Items -->
                <div class="outfit-items mb-3">
                    {% for item in outfit.items %}
                    <div class="outfit-item-mini">
                        {% if item.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                             alt="{{ item.name }}" class="outfit-item-image">
                        {% else %}
                        <div class="outfit-item-placeholder">
                            <i class="fas fa-tshirt"></i>
                        </div>
                        {% endif %}
                        <small>{{ item.name }}</small>
                    </div>
                    {% endfor %}
                </div>

                <!-- Outfit Stats -->
                <div class="outfit-stats mb-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.overall_score) }}</div>
                            <div class="stat-label">Overall</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.style_score) }}</div>
                            <div class="stat-label">Style</div>
                        </div>
                        <div class="col-4">
                            <div class="stat-value">{{ "%.1f"|format(outfit.color_harmony) }}</div>
                            <div class="stat-label">Harmony</div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                {% if outfit.description %}
                <p class="card-text text-muted small">{{ outfit.description }}</p>
                {% endif %}

                <!-- Weather and Occasion Tags -->
                <div class="mb-3">
                    <span class="badge bg-primary me-1">{{ outfit.weather.name.replace('_', ' ').title() }}</span>
                    <span class="badge bg-secondary">{{ outfit.occasion.name.replace('_', ' ').title() }}</span>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    <i class="fas fa-history fa-4x text-muted mb-4"></i>
    <h4>No Outfit History</h4>
    <p class="text-muted mb-4">You haven't generated any outfits yet. Start creating some outfits to see them here!</p>
    <a href="{{ url_for('generate_outfit') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-magic me-2"></i>Generate Your First Outfit
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.outfit-card {
    transition: transform 0.2s;
}

.outfit-card:hover {
    transform: translateY(-2px);
}

.outfit-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.outfit-item-mini {
    text-align: center;
    flex: 1;
    min-width: 60px;
}

.outfit-item-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.outfit-item-placeholder {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.outfit-item-mini small {
    display: block;
    margin-top: 4px;
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.outfit-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #dee2e6;
    cursor: pointer;
    transition: color 0.2s;
}

.favorite-btn:hover {
    color: #dc3545;
}

.favorite-btn.active {
    color: #dc3545;
}
</style>
{% endblock %}
