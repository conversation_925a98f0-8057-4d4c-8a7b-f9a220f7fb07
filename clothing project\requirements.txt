# Personal Closet Web Application - Python Dependencies
Flask==2.3.3
Werkzeug==2.3.7

# The web application requires Python 3.7+ and uses:
# - Flask for web framework
# - Werkzeug for file uploads and utilities
# - Built-in modules: dataclasses, enum, json, uuid, datetime
# - typing (built-in)
# - datetime (built-in)
# - random (built-in)
# - json (built-in)
# - sys (built-in)

# Optional dependencies for future enhancements:
# requests>=2.25.0        # For weather API integration
# colorama>=0.4.4         # For colored terminal output
# rich>=10.0.0           # For enhanced CLI formatting
# pillow>=8.0.0          # For future image processing
# numpy>=1.20.0          # For advanced color analysis
