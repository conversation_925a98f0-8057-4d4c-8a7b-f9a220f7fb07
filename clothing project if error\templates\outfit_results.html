{% extends "base.html" %}

{% block title %}Outfit Results - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-star me-3"></i>Your Perfect Outfits</h1>
    <p>AI-generated outfits for {{ occasion.value.title() }} in {{ weather.value.title() }} weather
    {% if preferred_style %} with {{ preferred_style.value.title() }} style{% endif %}</p>
</div>

<div id="outfitResults">
    {% if outfits %}
    <!-- Outfit Results -->
    <div class="row mb-4">
        {% for outfit in outfits %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card outfit-card h-100">
                <div class="position-relative">
                    <!-- Outfit Score -->
                    <div class="outfit-score">
                        {{ "%.0f"|format(outfit.overall_score * 100) }}%
                    </div>
                    
                    <!-- Favorite Button -->
                    <button class="favorite-btn" onclick="toggleFavorite('{{ outfit.id }}', this)">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-tshirt me-2"></i>Outfit {{ loop.index }}
                    </h5>
                    
                    <!-- Outfit Items Display -->
                    <div class="outfit-items">
                        {% for item in outfit.items %}
                            {% if item.photo_filename %}
                            <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                 class="outfit-item-thumb" alt="{{ item.name }}" title="{{ item.name }}"
                                 data-bs-toggle="tooltip" data-bs-placement="top">
                            {% else %}
                            <div class="outfit-item-thumb-placeholder" title="{{ item.name }}"
                                 data-bs-toggle="tooltip" data-bs-placement="top">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Outfit Description -->
                    <p class="card-text">{{ outfit.description }}</p>
                    
                    <!-- Item List -->
                    <div class="mb-3">
                        <h6 class="text-muted">Items:</h6>
                        <ul class="list-unstyled">
                            {% for item in outfit.items %}
                            <li class="d-flex justify-content-between align-items-center mb-1">
                                <span>
                                    <i class="fas fa-{{ 'tshirt' if item.category == 'top' else 'shoe-prints' if item.category == 'shoes' else 'vest' if item.category == 'outerwear' else 'user-tie' if item.category == 'dress' else 'circle' }} me-2 text-muted"></i>
                                    {{ item.name }}
                                </span>
                                <a href="{{ url_for('view_item', item_id=item.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <!-- Score Breakdown -->
                    <div class="score-breakdown mb-3">
                        <h6 class="text-muted">Score Breakdown:</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="small">
                                    <strong>{{ "%.0f"|format(outfit.style_score * 100) }}%</strong><br>
                                    <span class="text-muted">Style</span>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="small">
                                    <strong>{{ "%.0f"|format(outfit.color_harmony * 100) }}%</strong><br>
                                    <span class="text-muted">Colors</span>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="small">
                                    <strong>{{ "%.0f"|format(outfit.occasion_fit * 100) }}%</strong><br>
                                    <span class="text-muted">Occasion</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success flex-fill" 
                                onclick="saveOutfitFromGeneration({{ outfit.to_dict()|tojson }}, this)">
                            <i class="fas fa-save me-1"></i>Save Outfit
                        </button>
                        <button class="btn btn-outline-primary" 
                                onclick="shareOutfit('{{ outfit.id }}')"
                                data-bs-toggle="tooltip" title="Share Outfit">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Want More Options?</h5>
                    <div class="d-flex gap-2 justify-content-center flex-wrap">
                        <button class="btn btn-primary" onclick="regenerateOutfits()">
                            <i class="fas fa-redo me-2"></i>Generate More Outfits
                        </button>
                        <a href="{{ url_for('generate_outfit') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sliders-h me-2"></i>Change Preferences
                        </a>
                        <a href="{{ url_for('add_item') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-plus me-2"></i>Add More Items
                        </a>
                        <a href="{{ url_for('outfit_history') }}" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>View History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- No Outfits Found -->
    <div class="card text-center">
        <div class="card-body py-5">
            <i class="fas fa-search fa-4x text-muted mb-4"></i>
            <h4>No Suitable Outfits Found</h4>
            <p class="text-muted mb-4">
                We couldn't find any outfits that match your criteria. Here are some suggestions:
            </p>
            
            <div class="row text-start">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-plus-circle text-primary me-2"></i>Add More Items</h6>
                            <p class="small text-muted mb-2">
                                Your closet might need more variety for this occasion and weather.
                            </p>
                            <a href="{{ url_for('add_item') }}" class="btn btn-sm btn-primary">Add Items</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><i class="fas fa-sliders-h text-info me-2"></i>Adjust Preferences</h6>
                            <p class="small text-muted mb-2">
                                Try different occasion, weather, or style preferences.
                            </p>
                            <a href="{{ url_for('generate_outfit') }}" class="btn btn-sm btn-info">Change Settings</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>Current Search Criteria:</h6>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <span class="badge bg-primary">{{ occasion.value.title() }}</span>
                    <span class="badge bg-info">{{ weather.value.title() }}</span>
                    {% if preferred_style %}
                    <span class="badge bg-secondary">{{ preferred_style.value.title() }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share-alt me-2"></i>Share Outfit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Share this outfit with friends:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="copyOutfitLink()">
                        <i class="fas fa-copy me-2"></i>Copy Link
                    </button>
                    <button class="btn btn-outline-success" onclick="shareToWhatsApp()">
                        <i class="fab fa-whatsapp me-2"></i>Share to WhatsApp
                    </button>
                    <button class="btn btn-outline-info" onclick="shareToTwitter()">
                        <i class="fab fa-twitter me-2"></i>Share to Twitter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.outfit-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.outfit-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.outfit-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    z-index: 10;
}

.score-breakdown {
    font-size: 0.85rem;
}

.outfit-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 15px 0;
}

.outfit-item-thumb {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.outfit-item-thumb:hover {
    transform: scale(1.1);
}

.outfit-item-thumb-placeholder {
    width: 70px;
    height: 70px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #6c757d;
}

.favorite-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e17055;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 10;
}

.favorite-btn:hover {
    background: white;
    transform: scale(1.1);
}

.favorite-btn.active {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .outfit-items {
        justify-content: flex-start;
    }
    
    .outfit-item-thumb, .outfit-item-thumb-placeholder {
        width: 50px;
        height: 50px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function saveOutfitFromGeneration(outfitData, button) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(outfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
            showAlert('Outfit saved to your history!', 'success');
        } else {
            button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
            button.disabled = false;
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
        button.disabled = false;
        showAlert('Error saving outfit', 'error');
    });
}

function regenerateOutfits() {
    // Go back to generation page and auto-submit
    window.history.back();
}

function shareOutfit(outfitId) {
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
    
    // Store outfit ID for sharing functions
    window.currentOutfitId = outfitId;
}

function copyOutfitLink() {
    const url = `${window.location.origin}/outfit/${window.currentOutfitId}`;
    navigator.clipboard.writeText(url).then(() => {
        showAlert('Outfit link copied to clipboard!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('shareModal')).hide();
    }).catch(() => {
        showAlert('Failed to copy link', 'error');
    });
}

function shareToWhatsApp() {
    const text = `Check out this amazing outfit I created with my Personal Closet app!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

function shareToTwitter() {
    const text = `Just created an amazing outfit with my Personal Closet app! 👗✨ #PersonalStyle #OOTD`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
