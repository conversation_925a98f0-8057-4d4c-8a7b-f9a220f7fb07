{% extends "base.html" %}

{% block title %}Search Results - {{ query }} - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-search-dollar me-3"></i>Search Results</h1>
    <p>Found {{ total_results }} outfits for "<strong>{{ query }}</strong>"</p>
    
    <div class="search-meta">
        <span class="badge bg-primary me-2">{{ occasion.title() }}</span>
        <span class="badge bg-secondary me-2">{{ gender.title() }}</span>
        <span class="badge bg-success">{{ total_results }} results</span>
    </div>
</div>

<!-- Search Again Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <form method="GET" action="{{ url_for('search_outfits') }}" class="d-flex">
                            <input type="text" class="form-control me-2" name="query" 
                                   placeholder="Search for different style..." value="{{ query }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('search_outfits') }}" class="btn btn-secondary">
                            <i class="fas fa-plus me-1"></i>New Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if results %}
<!-- Filter and Sort Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div class="filter-options">
                <label class="form-label me-2">Sort by:</label>
                <select class="form-select form-select-sm d-inline-block w-auto" id="sortSelect">
                    <option value="relevance">Relevance</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                </select>
            </div>
            <div class="view-options">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm active" id="gridView">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="listView">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Grid -->
<div id="resultsContainer" class="row">
    {% for result in results %}
    <div class="col-lg-3 col-md-4 col-sm-6 mb-4 result-item" 
         data-price="{{ result.price }}" 
         data-rating="{{ result.rating or 0 }}"
         data-store="{{ result.store }}">
        <div class="card product-card h-100">
            <!-- Product Image -->
            <div class="position-relative">
                {% if result.image_url %}
                <img src="{{ result.image_url }}" class="card-img-top product-image" alt="{{ result.title }}">
                {% else %}
                <div class="product-image-placeholder">
                    <i class="fas fa-tshirt fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <!-- Discount Badge -->
                {% if result.discount %}
                <div class="discount-badge">{{ result.discount }}</div>
                {% endif %}
                
                <!-- Store Badge -->
                <div class="store-badge">{{ result.store }}</div>
            </div>
            
            <div class="card-body d-flex flex-column">
                <!-- Product Title -->
                <h6 class="card-title product-title">{{ result.title }}</h6>
                
                <!-- Rating -->
                {% if result.rating %}
                <div class="rating mb-2">
                    {% for i in range(5) %}
                        {% if i < result.rating %}
                        <i class="fas fa-star text-warning"></i>
                        {% else %}
                        <i class="far fa-star text-muted"></i>
                        {% endif %}
                    {% endfor %}
                    <small class="text-muted ms-1">
                        ({{ result.reviews_count or 0 }} reviews)
                    </small>
                </div>
                {% endif %}
                
                <!-- Description -->
                {% if result.description %}
                <p class="card-text text-muted small">{{ result.description[:100] }}{% if result.description|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                <!-- Price Section -->
                <div class="price-section mt-auto">
                    <div class="current-price">{{ result.price }}</div>
                    {% if result.original_price and result.original_price != result.price %}
                    <div class="original-price">{{ result.original_price }}</div>
                    {% endif %}
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-3">
                    <a href="{{ result.product_url }}" target="_blank" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-external-link-alt me-1"></i>View Product
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% else %}
<!-- No Results -->
<div class="text-center py-5">
    <div class="no-results">
        <i class="fas fa-search fa-4x text-muted mb-3"></i>
        <h3>No Results Found</h3>
        <p class="text-muted">We couldn't find any outfits matching "{{ query }}"</p>
        <div class="mt-4">
            <a href="{{ url_for('search_outfits') }}" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>Try Different Search
            </a>
        </div>
    </div>
</div>
{% endif %}

<style>
.search-meta {
    margin-top: 1rem;
}

.product-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.product-image-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
}

.store-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
}

.product-title {
    font-size: 0.9rem;
    line-height: 1.3;
    height: 2.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.rating {
    font-size: 0.8rem;
}

.price-section {
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #28a745;
}

.original-price {
    font-size: 0.9rem;
    color: #6c757d;
    text-decoration: line-through;
}

.filter-options .form-select {
    min-width: 150px;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

.no-results {
    padding: 3rem 0;
}

/* List view styles */
.list-view .result-item {
    width: 100%;
}

.list-view .product-card {
    flex-direction: row;
}

.list-view .product-image {
    width: 150px;
    height: 150px;
}

.list-view .card-body {
    flex: 1;
}
</style>

<script>
// Sort functionality
document.getElementById('sortSelect').addEventListener('change', function() {
    const sortBy = this.value;
    const container = document.getElementById('resultsContainer');
    const items = Array.from(container.children);
    
    items.sort((a, b) => {
        switch(sortBy) {
            case 'price-low':
                return parsePrice(a.dataset.price) - parsePrice(b.dataset.price);
            case 'price-high':
                return parsePrice(b.dataset.price) - parsePrice(a.dataset.price);
            case 'rating':
                return parseFloat(b.dataset.rating) - parseFloat(a.dataset.rating);
            default:
                return 0; // Keep original order for relevance
        }
    });
    
    items.forEach(item => container.appendChild(item));
});

// View toggle functionality
document.getElementById('gridView').addEventListener('click', function() {
    document.getElementById('resultsContainer').classList.remove('list-view');
    this.classList.add('active');
    document.getElementById('listView').classList.remove('active');
});

document.getElementById('listView').addEventListener('click', function() {
    document.getElementById('resultsContainer').classList.add('list-view');
    this.classList.add('active');
    document.getElementById('gridView').classList.remove('active');
});

// Helper function to parse price
function parsePrice(priceStr) {
    const match = priceStr.match(/[\d.]+/);
    return match ? parseFloat(match[0]) : 0;
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
