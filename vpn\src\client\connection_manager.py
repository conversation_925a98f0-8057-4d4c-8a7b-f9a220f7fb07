"""
Connection Manager for VPN Client
"""

import subprocess
import platform
from typing import Optional
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import VPNConfig
from ..utils.protocol import ConfigData

logger = get_logger(__name__)

class ConnectionManager:
    """Manages VPN client network configuration and routing"""
    
    def __init__(self, config: VPNConfig):
        self.config = config
        self.system = platform.system().lower()
        self.tun_interface: Optional[str] = None
        self.original_gateway: Optional[str] = None
        self.configured = False
        
        logger.info("Connection Manager initialized", system=self.system)
    
    def configure_network(self, config_data: ConfigData) -> bool:
        """Configure network with server-provided settings"""
        try:
            logger.info("Configuring network", 
                       ip=config_data.assigned_ip,
                       gateway=config_data.gateway)
            
            # Setup TUN interface
            if not self._setup_tun_interface(config_data):
                return False
            
            # Configure routing
            if not self._configure_routing(config_data):
                self._cleanup_tun_interface()
                return False
            
            # Configure DNS
            if not self._configure_dns(config_data):
                logger.warning("DNS configuration failed, continuing anyway")
            
            self.configured = True
            logger.info("Network configuration completed")
            return True
            
        except Exception as e:
            logger.error("Network configuration failed", error=str(e))
            self.cleanup()
            return False
    
    def cleanup(self) -> None:
        """Cleanup network configuration"""
        if not self.configured:
            return
        
        try:
            self._restore_dns()
            self._cleanup_routing()
            self._cleanup_tun_interface()
            
            self.configured = False
            logger.info("Network cleanup completed")
            
        except Exception as e:
            logger.error("Network cleanup failed", error=str(e))
    
    def route_to_local(self, packet_data: bytes) -> None:
        """Route packet to local network"""
        try:
            # In a real implementation, this would write the packet
            # to the TUN interface for the OS to handle
            logger.debug("Routing packet to local", size=len(packet_data))
            
        except Exception as e:
            logger.error("Local routing failed", error=str(e))
    
    def _setup_tun_interface(self, config_data: ConfigData) -> bool:
        """Setup TUN interface"""
        try:
            if self.system == "windows":
                return self._setup_tun_windows(config_data)
            elif self.system == "linux":
                return self._setup_tun_linux(config_data)
            elif self.system == "darwin":
                return self._setup_tun_macos(config_data)
            else:
                logger.error("Unsupported operating system", system=self.system)
                return False
                
        except Exception as e:
            logger.error("TUN interface setup failed", error=str(e))
            return False
    
    def _setup_tun_windows(self, config_data: ConfigData) -> bool:
        """Setup TUN interface on Windows"""
        try:
            # Windows TUN setup using TAP-Windows adapter
            logger.info("Setting up Windows TUN interface")
            
            # Find TAP adapter
            # This is simplified - real implementation would enumerate adapters
            self.tun_interface = "TAP-Windows Adapter"
            
            # Configure adapter
            subprocess.run([
                "netsh", "interface", "ip", "set", "address",
                self.tun_interface, "static",
                config_data.assigned_ip, config_data.subnet_mask
            ], check=True)
            
            logger.info("Windows TUN interface configured", 
                       interface=self.tun_interface,
                       ip=config_data.assigned_ip)
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("Windows TUN setup failed", error=str(e))
            return False
    
    def _setup_tun_linux(self, config_data: ConfigData) -> bool:
        """Setup TUN interface on Linux"""
        try:
            self.tun_interface = "vpn-client0"
            
            # Create TUN interface
            subprocess.run([
                "ip", "tuntap", "add", "dev", self.tun_interface, "mode", "tun"
            ], check=True)
            
            # Configure interface
            subprocess.run([
                "ip", "addr", "add", f"{config_data.assigned_ip}/24",
                "dev", self.tun_interface
            ], check=True)
            
            # Bring interface up
            subprocess.run([
                "ip", "link", "set", self.tun_interface, "up"
            ], check=True)
            
            logger.info("Linux TUN interface configured",
                       interface=self.tun_interface,
                       ip=config_data.assigned_ip)
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("Linux TUN setup failed", error=str(e))
            return False
    
    def _setup_tun_macos(self, config_data: ConfigData) -> bool:
        """Setup TUN interface on macOS"""
        try:
            self.tun_interface = "tun0"
            
            # Create and configure TUN interface
            subprocess.run([
                "ifconfig", self.tun_interface, "create"
            ], check=True)
            
            subprocess.run([
                "ifconfig", self.tun_interface,
                config_data.assigned_ip, "netmask", config_data.subnet_mask
            ], check=True)
            
            logger.info("macOS TUN interface configured",
                       interface=self.tun_interface,
                       ip=config_data.assigned_ip)
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("macOS TUN setup failed", error=str(e))
            return False
    
    def _configure_routing(self, config_data: ConfigData) -> bool:
        """Configure routing rules"""
        try:
            # Save original default gateway
            self.original_gateway = self._get_default_gateway()
            
            if self.system == "windows":
                return self._configure_routing_windows(config_data)
            elif self.system == "linux":
                return self._configure_routing_linux(config_data)
            elif self.system == "darwin":
                return self._configure_routing_macos(config_data)
            else:
                return False
                
        except Exception as e:
            logger.error("Routing configuration failed", error=str(e))
            return False
    
    def _configure_routing_windows(self, config_data: ConfigData) -> bool:
        """Configure routing on Windows"""
        try:
            # Add route for VPN gateway
            subprocess.run([
                "route", "add", "0.0.0.0", "mask", "0.0.0.0", config_data.gateway
            ], check=True)
            
            logger.info("Windows routing configured")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("Windows routing failed", error=str(e))
            return False
    
    def _configure_routing_linux(self, config_data: ConfigData) -> bool:
        """Configure routing on Linux"""
        try:
            # Add default route through VPN
            subprocess.run([
                "ip", "route", "add", "default", "via", config_data.gateway,
                "dev", self.tun_interface
            ], check=True)
            
            logger.info("Linux routing configured")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("Linux routing failed", error=str(e))
            return False
    
    def _configure_routing_macos(self, config_data: ConfigData) -> bool:
        """Configure routing on macOS"""
        try:
            # Add default route through VPN
            subprocess.run([
                "route", "add", "default", config_data.gateway
            ], check=True)
            
            logger.info("macOS routing configured")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("macOS routing failed", error=str(e))
            return False
    
    def _configure_dns(self, config_data: ConfigData) -> bool:
        """Configure DNS settings"""
        try:
            if self.system == "windows":
                return self._configure_dns_windows(config_data)
            elif self.system == "linux":
                return self._configure_dns_linux(config_data)
            elif self.system == "darwin":
                return self._configure_dns_macos(config_data)
            else:
                return False
                
        except Exception as e:
            logger.error("DNS configuration failed", error=str(e))
            return False
    
    def _configure_dns_windows(self, config_data: ConfigData) -> bool:
        """Configure DNS on Windows"""
        try:
            for dns in config_data.dns_servers:
                subprocess.run([
                    "netsh", "interface", "ip", "set", "dns",
                    self.tun_interface, "static", dns
                ], check=True)
            
            logger.info("Windows DNS configured", dns_servers=config_data.dns_servers)
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("Windows DNS configuration failed", error=str(e))
            return False
    
    def _configure_dns_linux(self, config_data: ConfigData) -> bool:
        """Configure DNS on Linux"""
        try:
            # Backup original resolv.conf
            subprocess.run([
                "cp", "/etc/resolv.conf", "/etc/resolv.conf.vpn-backup"
            ], check=True)
            
            # Write new resolv.conf
            with open("/etc/resolv.conf", "w") as f:
                for dns in config_data.dns_servers:
                    f.write(f"nameserver {dns}\n")
            
            logger.info("Linux DNS configured", dns_servers=config_data.dns_servers)
            return True
            
        except Exception as e:
            logger.error("Linux DNS configuration failed", error=str(e))
            return False
    
    def _configure_dns_macos(self, config_data: ConfigData) -> bool:
        """Configure DNS on macOS"""
        try:
            dns_list = " ".join(config_data.dns_servers)
            subprocess.run([
                "networksetup", "-setdnsservers", "Wi-Fi", dns_list
            ], check=True)
            
            logger.info("macOS DNS configured", dns_servers=config_data.dns_servers)
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error("macOS DNS configuration failed", error=str(e))
            return False
    
    def _get_default_gateway(self) -> Optional[str]:
        """Get current default gateway"""
        try:
            if self.system == "windows":
                result = subprocess.run([
                    "route", "print", "0.0.0.0"
                ], capture_output=True, text=True, check=True)
                # Parse Windows route output
                # This is simplified - real implementation would parse properly
                return "***********"  # Placeholder
                
            elif self.system == "linux":
                result = subprocess.run([
                    "ip", "route", "show", "default"
                ], capture_output=True, text=True, check=True)
                # Parse: default via *********** dev eth0
                parts = result.stdout.split()
                if len(parts) >= 3 and parts[1] == "via":
                    return parts[2]
                    
            elif self.system == "darwin":
                result = subprocess.run([
                    "route", "get", "default"
                ], capture_output=True, text=True, check=True)
                # Parse macOS route output
                for line in result.stdout.split('\n'):
                    if 'gateway:' in line:
                        return line.split(':')[1].strip()
            
            return None
            
        except Exception as e:
            logger.error("Failed to get default gateway", error=str(e))
            return None
    
    def _cleanup_tun_interface(self) -> None:
        """Cleanup TUN interface"""
        if not self.tun_interface:
            return
        
        try:
            if self.system == "linux":
                subprocess.run([
                    "ip", "link", "delete", self.tun_interface
                ], check=False)
            elif self.system == "darwin":
                subprocess.run([
                    "ifconfig", self.tun_interface, "destroy"
                ], check=False)
            
            logger.info("TUN interface cleaned up", interface=self.tun_interface)
            
        except Exception as e:
            logger.error("TUN cleanup failed", error=str(e))
    
    def _cleanup_routing(self) -> None:
        """Cleanup routing rules"""
        try:
            if self.original_gateway and self.system == "linux":
                subprocess.run([
                    "ip", "route", "add", "default", "via", self.original_gateway
                ], check=False)
            
            logger.info("Routing cleaned up")
            
        except Exception as e:
            logger.error("Routing cleanup failed", error=str(e))
    
    def _restore_dns(self) -> None:
        """Restore original DNS settings"""
        try:
            if self.system == "linux":
                subprocess.run([
                    "mv", "/etc/resolv.conf.vpn-backup", "/etc/resolv.conf"
                ], check=False)
            
            logger.info("DNS restored")
            
        except Exception as e:
            logger.error("DNS restore failed", error=str(e))
