{% extends "base.html" %}

{% block title %}{{ item.name }} - My Personal Closet{% endblock %}

{% block content %}
<div class="row">
    <!-- Item Photo and Basic Info -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body text-center">
                {% if item.photo_filename %}
                <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                     class="img-fluid rounded" style="max-height: 400px; object-fit: cover;" alt="{{ item.name }}">
                {% else %}
                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                     style="height: 400px;">
                    <i class="fas fa-tshirt fa-5x text-muted"></i>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <h3>{{ item.name }}</h3>
                    <p class="text-muted">{{ item.category.replace('_', ' ').title() }}</p>
                    
                    <!-- Condition Badge -->
                    <span class="badge {{ 'bg-success' if item.condition == 'excellent' else 
                                        'bg-primary' if item.condition == 'good' else 
                                        'bg-warning' if item.condition == 'fair' else 'bg-danger' }} fs-6">
                        {{ item.condition.title() }} Condition
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Item Details -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Item Details
                </h5>
            </div>
            <div class="card-body">
                <!-- Basic Information -->
                <div class="row mb-3">
                    {% if item.brand %}
                    <div class="col-sm-6">
                        <strong>Brand:</strong><br>
                        <span class="text-muted">{{ item.brand }}</span>
                    </div>
                    {% endif %}
                    {% if item.size %}
                    <div class="col-sm-6">
                        <strong>Size:</strong><br>
                        <span class="text-muted">{{ item.size }}</span>
                    </div>
                    {% endif %}
                </div>
                
                {% if item.material %}
                <div class="mb-3">
                    <strong>Material:</strong><br>
                    <span class="text-muted">{{ item.material }}</span>
                </div>
                {% endif %}
                
                <!-- Colors -->
                <div class="mb-3">
                    <strong>Colors:</strong><br>
                    {% for color in item.colors %}
                    <span class="badge bg-secondary me-1">{{ color }}</span>
                    {% endfor %}
                </div>
                
                <!-- Styles -->
                <div class="mb-3">
                    <strong>Styles:</strong><br>
                    {% for style in item.style %}
                    <span class="badge bg-light text-dark me-1">{{ style.value.replace('_', ' ').title() }}</span>
                    {% endfor %}
                </div>
                
                <!-- Occasions -->
                <div class="mb-3">
                    <strong>Suitable Occasions:</strong><br>
                    {% for occasion in item.occasions %}
                    <span class="badge bg-primary me-1">{{ occasion.value.replace('_', ' ').title() }}</span>
                    {% endfor %}
                </div>
                
                <!-- Weather -->
                <div class="mb-3">
                    <strong>Weather Conditions:</strong><br>
                    {% for weather in item.weather %}
                    <span class="badge bg-info me-1">{{ weather.value.replace('_', ' ').title() }}</span>
                    {% endfor %}
                </div>
                
                <!-- Ratings -->
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <strong>Formality Level:</strong><br>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ item.formality * 10 }}%">
                                {{ item.formality }}/10
                            </div>
                        </div>
                        <small class="text-muted">Casual ← → Formal</small>
                    </div>
                    <div class="col-sm-6">
                        <strong>Versatility:</strong><br>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ item.versatility * 10 }}%">
                                {{ item.versatility }}/10
                            </div>
                        </div>
                        <small class="text-muted">Specific ← → Versatile</small>
                    </div>
                </div>
                
                <!-- Purchase Information -->
                {% if item.purchase_date or item.cost > 0 %}
                <div class="row mb-3">
                    {% if item.purchase_date %}
                    <div class="col-sm-6">
                        <strong>Purchase Date:</strong><br>
                        <span class="text-muted">{{ item.purchase_date }}</span>
                    </div>
                    {% endif %}
                    {% if item.cost > 0 %}
                    <div class="col-sm-6">
                        <strong>Cost:</strong><br>
                        <span class="text-muted">${{ "%.2f"|format(item.cost) }}</span>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Wear Statistics -->
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <strong>Times Worn:</strong><br>
                        <span class="text-muted">{{ item.wear_count }} times</span>
                    </div>
                    {% if item.last_worn %}
                    <div class="col-sm-6">
                        <strong>Last Worn:</strong><br>
                        <span class="text-muted">{{ item.last_worn[:10] }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Notes -->
                {% if item.notes %}
                <div class="mb-3">
                    <strong>Notes:</strong><br>
                    <p class="text-muted">{{ item.notes }}</p>
                </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class="d-flex gap-2 mt-4">
                    <a href="{{ url_for('edit_item', item_id=item.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>Edit Item
                    </a>
                    <button class="btn btn-outline-danger" onclick="confirmDelete('{{ item.id }}', '{{ item.name }}')">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                    <a href="{{ url_for('view_closet') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Closet
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Outfit History -->
{% if outfit_history %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Outfit History
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">This item has been used in {{ outfit_history|length }} outfit(s):</p>
                
                <div class="row">
                    {% for outfit in outfit_history[:6] %}
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">{{ outfit.occasion.value.title() }} Outfit</h6>
                                
                                <div class="outfit-preview mb-2">
                                    {% for outfit_item in outfit.items[:4] %}
                                        {% if outfit_item.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/' + outfit_item.photo_filename) }}" 
                                             class="outfit-item" alt="{{ outfit_item.name }}" title="{{ outfit_item.name }}">
                                        {% else %}
                                        <div class="outfit-item-placeholder" title="{{ outfit_item.name }}">
                                            <i class="fas fa-tshirt"></i>
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ outfit.date_created[:10] }}</small>
                                    <span class="badge bg-primary">{{ "%.0f"|format(outfit.overall_score * 100) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if outfit_history|length > 6 %}
                <div class="text-center">
                    <a href="{{ url_for('outfit_history') }}" class="btn btn-outline-primary">
                        View All Outfits <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteItemName"></strong>?</p>
                <p class="text-muted small">This action cannot be undone and will remove the item from all outfit history.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Item
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemId, itemName) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = `/delete-item/${itemId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
