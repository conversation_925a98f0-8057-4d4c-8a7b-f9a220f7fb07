# VPN Server Configuration

server:
  host: "0.0.0.0"              # Listen on all interfaces
  port: 1194                   # Standard OpenVPN port
  protocol: "udp"              # Protocol: udp or tcp
  max_clients: 100             # Maximum concurrent clients
  keepalive_interval: 10       # Keepalive ping interval (seconds)
  keepalive_timeout: 120       # Client timeout (seconds)

network:
  subnet: "********/24"        # VPN subnet
  dns_servers:                 # DNS servers to push to clients
    - "*******"
    - "*******"
  routes: []                   # Additional routes to push
  redirect_gateway: true       # Redirect all traffic through VPN

security:
  cert_file: "config/certificates/server.crt"
  key_file: "config/certificates/server.key"
  ca_file: "config/certificates/ca.crt"
  dh_file: "config/certificates/dh.pem"
  cipher: "AES-256-GCM"        # Encryption cipher
  auth: "SHA256"               # Authentication algorithm
  tls_version_min: "1.2"       # Minimum TLS version

logging:
  level: "INFO"                # Log level: DEBUG, INFO, WARNING, ERROR
  file: "logs/vpn-server.log"  # Log file path
  max_size: 10485760          # Max log file size (10MB)
  backup_count: 5             # Number of backup log files
