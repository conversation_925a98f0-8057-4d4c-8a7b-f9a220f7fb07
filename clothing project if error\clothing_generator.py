"""
Stylish Clothing Generator
A comprehensive outfit generator that creates stylish, coordinated outfits
based on occasion, weather, personal style, and fashion rules.
"""

import random
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class Occasion(Enum):
    CASUAL = "casual"
    BUSINESS = "business"
    FORMAL = "formal"
    PARTY = "party"
    WORKOUT = "workout"
    DATE = "date"
    BEACH = "beach"
    TRAVEL = "travel"


class Weather(Enum):
    HOT = "hot"
    WARM = "warm"
    MILD = "mild"
    COOL = "cool"
    COLD = "cold"
    RAINY = "rainy"


class Style(Enum):
    CLASSIC = "classic"
    TRENDY = "trendy"
    BOHEMIAN = "bohemian"
    MINIMALIST = "minimalist"
    EDGY = "edgy"
    ROMANTIC = "romantic"
    SPORTY = "sporty"
    CASUAL = "casual"
    BUSINESS = "business"


@dataclass
class ClothingItem:
    name: str
    category: str
    colors: List[str]
    style: List[Style]
    occasions: List[Occasion]
    weather: List[Weather]
    formality: int  # 1-10 scale (1=very casual, 10=very formal)
    versatility: int  # 1-10 scale (1=specific use, 10=very versatile)


@dataclass
class Outfit:
    items: List[ClothingItem]
    style_score: float
    color_harmony: float
    occasion_fit: float
    overall_score: float
    description: str


class ColorHarmony:
    """Color coordination and harmony rules"""
    
    # Color families and their complementary colors
    COLOR_FAMILIES = {
        'neutral': ['black', 'white', 'gray', 'beige', 'cream', 'brown', 'navy'],
        'warm': ['red', 'orange', 'yellow', 'coral', 'burgundy', 'gold'],
        'cool': ['blue', 'green', 'purple', 'teal', 'mint', 'lavender'],
        'earth': ['brown', 'tan', 'olive', 'rust', 'terracotta', 'khaki']
    }
    
    # Colors that work well together
    COMPLEMENTARY_PAIRS = [
        ('blue', 'orange'), ('red', 'green'), ('yellow', 'purple'),
        ('navy', 'coral'), ('burgundy', 'cream'), ('black', 'gold')
    ]
    
    # Safe color combinations
    SAFE_COMBINATIONS = [
        ['black', 'white'], ['navy', 'white'], ['gray', 'white'],
        ['beige', 'brown'], ['navy', 'beige'], ['black', 'gray'],
        ['denim', 'white'], ['khaki', 'white'], ['burgundy', 'cream']
    ]
    
    @classmethod
    def calculate_harmony(cls, colors: List[str]) -> float:
        """Calculate color harmony score (0-1)"""
        if len(colors) <= 1:
            return 1.0
        
        score = 0.0
        total_pairs = 0
        
        for i in range(len(colors)):
            for j in range(i + 1, len(colors)):
                color1, color2 = colors[i].lower(), colors[j].lower()
                total_pairs += 1
                
                # Check if colors are in the same family
                same_family = any(
                    color1 in family and color2 in family 
                    for family in cls.COLOR_FAMILIES.values()
                )
                
                # Check if colors are complementary
                is_complementary = any(
                    (color1 in pair and color2 in pair) 
                    for pair in cls.COMPLEMENTARY_PAIRS
                )
                
                # Check if it's a safe combination
                is_safe = any(
                    set([color1, color2]).issubset(set(combo))
                    for combo in cls.SAFE_COMBINATIONS
                )
                
                if same_family:
                    score += 0.8
                elif is_complementary:
                    score += 0.9
                elif is_safe:
                    score += 0.7
                elif 'neutral' in [cls._get_color_family(color1), cls._get_color_family(color2)]:
                    score += 0.6
                else:
                    score += 0.3
        
        return score / total_pairs if total_pairs > 0 else 1.0
    
    @classmethod
    def _get_color_family(cls, color: str) -> str:
        """Get the color family for a given color"""
        for family, colors in cls.COLOR_FAMILIES.items():
            if color.lower() in colors:
                return family
        return 'other'


class ClothingDatabase:
    """Database of clothing items with their attributes"""
    
    def __init__(self):
        self.items = self._initialize_clothing_items()
    
    def _initialize_clothing_items(self) -> List[ClothingItem]:
        """Initialize the clothing database with various items"""
        items = []
        
        # Tops
        items.extend([
            ClothingItem("White Button-Down Shirt", "top", ["white"], 
                        [Style.CLASSIC, Style.MINIMALIST], 
                        [Occasion.BUSINESS, Occasion.CASUAL, Occasion.DATE],
                        [Weather.MILD, Weather.COOL, Weather.WARM], 8, 10),
            
            ClothingItem("Black Turtleneck", "top", ["black"], 
                        [Style.CLASSIC, Style.MINIMALIST, Style.EDGY], 
                        [Occasion.BUSINESS, Occasion.CASUAL, Occasion.DATE],
                        [Weather.COOL, Weather.COLD], 7, 9),
            
            ClothingItem("Silk Blouse", "top", ["cream", "navy", "burgundy"], 
                        [Style.CLASSIC, Style.ROMANTIC], 
                        [Occasion.BUSINESS, Occasion.FORMAL, Occasion.DATE],
                        [Weather.MILD, Weather.WARM], 8, 8),
            
            ClothingItem("Graphic T-Shirt", "top", ["white", "black", "gray"], 
                        [Style.CASUAL, Style.EDGY], 
                        [Occasion.CASUAL, Occasion.TRAVEL],
                        [Weather.HOT, Weather.WARM], 3, 7),
            
            ClothingItem("Cashmere Sweater", "top", ["beige", "gray", "navy"], 
                        [Style.CLASSIC, Style.MINIMALIST], 
                        [Occasion.CASUAL, Occasion.BUSINESS],
                        [Weather.COOL, Weather.COLD], 6, 8),
        ])
        
        # Bottoms
        items.extend([
            ClothingItem("Dark Wash Jeans", "bottom", ["denim"], 
                        [Style.CASUAL, Style.CLASSIC], 
                        [Occasion.CASUAL, Occasion.DATE, Occasion.TRAVEL],
                        [Weather.MILD, Weather.COOL], 4, 10),
            
            ClothingItem("Black Trousers", "bottom", ["black"], 
                        [Style.CLASSIC, Style.MINIMALIST], 
                        [Occasion.BUSINESS, Occasion.FORMAL],
                        [Weather.MILD, Weather.COOL, Weather.COLD], 8, 9),
            
            ClothingItem("Pencil Skirt", "bottom", ["black", "navy", "gray"], 
                        [Style.CLASSIC, Style.BUSINESS], 
                        [Occasion.BUSINESS, Occasion.FORMAL],
                        [Weather.MILD, Weather.WARM], 8, 7),
            
            ClothingItem("Midi Dress", "dress", ["navy", "black", "burgundy"], 
                        [Style.CLASSIC, Style.ROMANTIC], 
                        [Occasion.DATE, Occasion.PARTY, Occasion.BUSINESS],
                        [Weather.WARM, Weather.MILD], 7, 8),
        ])
        
        # Outerwear
        items.extend([
            ClothingItem("Blazer", "outerwear", ["navy", "black", "gray"], 
                        [Style.CLASSIC, Style.BUSINESS], 
                        [Occasion.BUSINESS, Occasion.FORMAL, Occasion.DATE],
                        [Weather.MILD, Weather.COOL], 9, 9),
            
            ClothingItem("Leather Jacket", "outerwear", ["black", "brown"], 
                        [Style.EDGY, Style.CASUAL], 
                        [Occasion.CASUAL, Occasion.DATE, Occasion.PARTY],
                        [Weather.COOL, Weather.MILD], 6, 7),
            
            ClothingItem("Trench Coat", "outerwear", ["beige", "navy"],
                        [Style.CLASSIC, Style.MINIMALIST],
                        [Occasion.BUSINESS, Occasion.CASUAL, Occasion.TRAVEL],
                        [Weather.COOL, Weather.RAINY], 8, 9),
        ])
        
        # Shoes
        items.extend([
            ClothingItem("Black Pumps", "shoes", ["black"], 
                        [Style.CLASSIC, Style.BUSINESS], 
                        [Occasion.BUSINESS, Occasion.FORMAL, Occasion.DATE],
                        [Weather.MILD, Weather.WARM], 9, 8),
            
            ClothingItem("White Sneakers", "shoes", ["white"], 
                        [Style.CASUAL, Style.SPORTY, Style.MINIMALIST], 
                        [Occasion.CASUAL, Occasion.TRAVEL, Occasion.WORKOUT],
                        [Weather.HOT, Weather.WARM, Weather.MILD], 3, 10),
            
            ClothingItem("Ankle Boots", "shoes", ["black", "brown"], 
                        [Style.EDGY, Style.CASUAL], 
                        [Occasion.CASUAL, Occasion.DATE],
                        [Weather.COOL, Weather.COLD], 6, 8),
        ])
        
        return items
    
    def get_items_by_category(self, category: str) -> List[ClothingItem]:
        """Get all items in a specific category"""
        return [item for item in self.items if item.category == category]
    
    def get_items_by_occasion(self, occasion: Occasion) -> List[ClothingItem]:
        """Get all items suitable for a specific occasion"""
        return [item for item in self.items if occasion in item.occasions]
    
    def get_items_by_weather(self, weather: Weather) -> List[ClothingItem]:
        """Get all items suitable for specific weather"""
        return [item for item in self.items if weather in item.weather]


class OutfitGenerator:
    """Main outfit generation engine"""

    def __init__(self):
        self.database = ClothingDatabase()
        self.color_harmony = ColorHarmony()

    def generate_outfit(self,
                       occasion: Occasion = Occasion.CASUAL,
                       weather: Weather = Weather.MILD,
                       preferred_style: Optional[Style] = None,
                       color_preference: Optional[str] = None,
                       num_suggestions: int = 3) -> List[Outfit]:
        """Generate stylish outfit suggestions"""

        # Filter items based on occasion and weather
        suitable_items = self._filter_items(occasion, weather, preferred_style)

        # Generate outfit combinations
        outfit_combinations = self._generate_combinations(suitable_items, color_preference)

        # Score and rank outfits
        scored_outfits = []
        for combination in outfit_combinations:
            outfit = self._score_outfit(combination, occasion, preferred_style)
            if outfit.overall_score > 0.5:  # Only include decent outfits
                scored_outfits.append(outfit)

        # Sort by overall score and return top suggestions
        scored_outfits.sort(key=lambda x: x.overall_score, reverse=True)
        return scored_outfits[:num_suggestions]

    def _filter_items(self, occasion: Occasion, weather: Weather,
                     preferred_style: Optional[Style]) -> Dict[str, List[ClothingItem]]:
        """Filter clothing items based on criteria"""
        filtered = {
            'tops': [],
            'bottoms': [],
            'dresses': [],
            'outerwear': [],
            'shoes': []
        }

        for item in self.database.items:
            # Check occasion and weather compatibility
            if occasion not in item.occasions or weather not in item.weather:
                continue

            # Check style preference if specified
            if preferred_style and preferred_style not in item.style:
                continue

            # Categorize items
            if item.category == 'top':
                filtered['tops'].append(item)
            elif item.category == 'bottom':
                filtered['bottoms'].append(item)
            elif item.category == 'dress':
                filtered['dresses'].append(item)
            elif item.category == 'outerwear':
                filtered['outerwear'].append(item)
            elif item.category == 'shoes':
                filtered['shoes'].append(item)

        return filtered

    def _generate_combinations(self, filtered_items: Dict[str, List[ClothingItem]],
                             color_preference: Optional[str]) -> List[List[ClothingItem]]:
        """Generate valid outfit combinations"""
        combinations = []

        # Generate dress-based outfits
        for dress in filtered_items['dresses']:
            for shoes in filtered_items['shoes']:
                combo = [dress, shoes]

                # Add outerwear if available and weather appropriate
                if filtered_items['outerwear']:
                    for outerwear in filtered_items['outerwear'][:2]:  # Limit options
                        combinations.append(combo + [outerwear])
                else:
                    combinations.append(combo)

        # Generate top + bottom combinations
        for top in filtered_items['tops']:
            for bottom in filtered_items['bottoms']:
                for shoes in filtered_items['shoes']:
                    combo = [top, bottom, shoes]

                    # Add outerwear if available
                    if filtered_items['outerwear']:
                        for outerwear in filtered_items['outerwear'][:2]:
                            combinations.append(combo + [outerwear])
                    else:
                        combinations.append(combo)

        # Filter by color preference if specified
        if color_preference:
            combinations = [
                combo for combo in combinations
                if any(color_preference.lower() in item.colors for item in combo)
            ]

        return combinations[:20]  # Limit to prevent too many combinations

    def _score_outfit(self, items: List[ClothingItem], occasion: Occasion,
                     preferred_style: Optional[Style]) -> Outfit:
        """Score an outfit based on various criteria"""

        # Extract colors from all items
        all_colors = []
        for item in items:
            all_colors.extend(item.colors)

        # Calculate scores
        color_harmony = self.color_harmony.calculate_harmony(all_colors)
        style_score = self._calculate_style_score(items, preferred_style)
        occasion_fit = self._calculate_occasion_fit(items, occasion)

        # Calculate overall score (weighted average)
        overall_score = (
            color_harmony * 0.3 +
            style_score * 0.4 +
            occasion_fit * 0.3
        )

        # Generate description
        description = self._generate_description(items, overall_score)

        return Outfit(
            items=items,
            style_score=style_score,
            color_harmony=color_harmony,
            occasion_fit=occasion_fit,
            overall_score=overall_score,
            description=description
        )

    def _calculate_style_score(self, items: List[ClothingItem],
                              preferred_style: Optional[Style]) -> float:
        """Calculate how well items work together stylistically"""
        if not items:
            return 0.0

        # Get all styles represented in the outfit
        all_styles = set()
        for item in items:
            all_styles.update(item.style)

        # Base score on style consistency
        style_consistency = 1.0 / len(all_styles) if all_styles else 0.0

        # Bonus for preferred style
        preferred_bonus = 0.0
        if preferred_style:
            style_match_count = sum(
                1 for item in items if preferred_style in item.style
            )
            preferred_bonus = style_match_count / len(items)

        return min(1.0, style_consistency + preferred_bonus * 0.3)

    def _calculate_occasion_fit(self, items: List[ClothingItem], occasion: Occasion) -> float:
        """Calculate how appropriate the outfit is for the occasion"""
        if not items:
            return 0.0

        # Check if all items are appropriate for the occasion
        appropriate_items = sum(1 for item in items if occasion in item.occasions)
        return appropriate_items / len(items)

    def _generate_description(self, items: List[ClothingItem], score: float) -> str:
        """Generate a description of the outfit"""
        item_names = [item.name for item in items]

        if score >= 0.8:
            quality = "Excellent"
        elif score >= 0.7:
            quality = "Great"
        elif score >= 0.6:
            quality = "Good"
        else:
            quality = "Decent"

        return f"{quality} outfit: {', '.join(item_names)}"
