{"version": 3, "file": "util-entitlements.js", "sourceRoot": "", "sources": ["../../src/util-entitlements.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,uCAAyB;AACzB,2CAA6B;AAC7B,6CAA+B;AAE/B,iCAAsD;AAStD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE9C;;;;;;;GAOG;AACI,KAAK,UAAU,mBAAmB,CACvC,IAA0B,EAC1B,WAA+B,EAC/B,QAAyB;;IAEzB,IAAI,CAAC,WAAW,CAAC,YAAY;QAAE,OAAO;IAEtC,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjE,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;QAAE,OAAO,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAE9D,qEAAqE;IACrE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAA,yBAAkB,EAAC,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC;IAEtE,IAAA,eAAQ,EACN,qCAAqC,EACrC,IAAI,EACJ,eAAe,EACf,WAAW,EACX,IAAI,CACL,CAAC;IACF,IAAI,YAAiC,CAAC;IACtC,IAAI,OAAO,WAAW,CAAC,YAAY,KAAK,QAAQ,EAAE;QAChD,MAAM,oBAAoB,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACjF,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,oBAAoB,CAAwB,CAAC;KACzE;SAAM;QACL,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,CAAsB,CAAC,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC,iCACzF,IAAI,KACP,CAAC,cAAc,CAAC,EAAE,IAAI,IACtB,EAAE,EAAE,CAAC,CAAC;KACT;IACD,IAAI,CAAC,YAAY,CAAC,gCAAgC,CAAC,EAAE;QACnD,iDAAiD;QACjD,OAAO;KACR;IAED,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC/D,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,eAAe,CAAwB,CAAC;IACpE,wDAAwD;IACxD,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,IAAA,eAAQ,EAAC,0CAA0C,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;KAC/E;SAAM;QACL,gEAAgE;QAChE,IAAI,QAAQ,CAAC,mBAAmB,EAAE;YAChC,OAAO,CAAC,cAAc;gBACpB,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,qCAAqC,CAAC,CAAC;YAC3F,IAAA,eAAQ,EACN,oFAAoF;gBAClF,OAAO,CAAC,cAAc,CACzB,CAAC;SACH;aAAM;YACL,MAAM,MAAM,GAAG,MAAA,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,0CAAG,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,mEAAmE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAC9G;YACD,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC;YAChC,IAAA,eAAQ,EACN,gFAAgF;gBAC9E,OAAO,CAAC,cAAc,CACzB,CAAC;SACH;QACD,MAAM,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC;QAE9D,IAAA,eAAQ,EAAC,uBAAuB,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;KACvE;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,GAAG,GAAG,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAChF,8CAA8C;IAC9C,IAAI,YAAY,CAAC,kCAAkC,CAAC,EAAE;QACpD,IAAA,eAAQ,EACN,iEAAiE;YAC/D,YAAY,CAAC,kCAAkC,CAAC,CACnD,CAAC;KACH;SAAM;QACL,IAAA,eAAQ,EACN,mFAAmF;YACjF,aAAa,CAChB,CAAC;QACF,YAAY,CAAC,kCAAkC,CAAC,GAAG,aAAa,CAAC;KAClE;IACD,iDAAiD;IACjD,IAAI,YAAY,CAAC,qCAAqC,CAAC,EAAE;QACvD,IAAA,eAAQ,EACN,oEAAoE;YAClE,YAAY,CAAC,qCAAqC,CAAC,CACtD,CAAC;KACH;SAAM;QACL,IAAA,eAAQ,EACN,sFAAsF;YACpF,OAAO,CAAC,cAAc,CACzB,CAAC;QACF,YAAY,CAAC,qCAAqC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;KAC9E;IACD,yDAAyD;IACzD,IAAI,CAAC,YAAY,CAAC,uCAAuC,CAAC,EAAE;QAC1D,YAAY,CAAC,uCAAuC,CAAC,GAAG,EAAE,CAAC;KAC5D;IACD,iCAAiC;IACjC,IACE,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,uCAAuC,CAAC,CAAC;QACpE,YAAY,CAAC,uCAAuC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EACnF;QACA,IAAA,eAAQ,EACN,wFAAwF;YACtF,aAAa,CAChB,CAAC;QACF,YAAY,CAAC,uCAAuC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC3E;SAAM;QACL,IAAA,eAAQ,EACN,sEAAsE,GAAG,aAAa,CACvF,CAAC;KACH;IACD,qCAAqC;IACrC,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAC7E,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;IAC9D,MAAM,EAAE,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,CAAC;IACxE,IAAA,eAAQ,EAAC,4BAA4B,EAAE,IAAI,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAElF,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAC3C,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAvHD,kDAuHC"}