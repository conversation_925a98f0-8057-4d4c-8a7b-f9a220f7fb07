@echo off
REM Personal Closet Website - Docker Launcher for Windows
REM Easy startup script using Docker

echo.
echo ========================================
echo 👗 Personal Closet Website (Docker)
echo ========================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not running
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker is available

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Create data directory if it doesn't exist
if not exist "data" mkdir data
if not exist "static\uploads" mkdir static\uploads

echo.
echo 🐳 Building and starting Personal Closet Website...
echo 📱 The website will be available at: http://localhost:5000
echo ⏹️  Press Ctrl+C to stop the application
echo.

REM Build and run with docker-compose
docker-compose up --build

echo.
echo 👋 Personal Closet Website stopped
pause
