{% extends "base.html" %}

{% block title %}Search Online Outfits - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-search me-3"></i>Search Online Outfits</h1>
    <p>Find the best outfits online with prices from top fashion retailers</p>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card search-card">
            <div class="card-body">
                <form id="searchForm" method="POST" action="{{ url_for('search_outfits') }}">
                    <div class="row mb-4">
                        <div class="col-12">
                            <label for="style_query" class="form-label">
                                <i class="fas fa-magic me-2"></i>What style are you looking for?
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="style_query" 
                                   name="style_query" 
                                   placeholder="e.g., casual summer dress, business formal suit, cozy winter sweater..."
                                   required>
                            <div class="form-text">
                                Be specific! Include colors, materials, or specific items you want.
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label for="occasion" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Occasion
                            </label>
                            <select class="form-select" id="occasion" name="occasion">
                                {% for occasion in occasions %}
                                <option value="{{ occasion.value }}" 
                                        {% if occasion.value == 'casual' %}selected{% endif %}>
                                    {{ occasion.value.title() }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="gender" class="form-label">
                                <i class="fas fa-user me-2"></i>Gender
                            </label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="unisex" selected>Unisex</option>
                                <option value="women">Women</option>
                                <option value="men">Men</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="max_results" class="form-label">
                                <i class="fas fa-list me-2"></i>Max Results
                            </label>
                            <select class="form-select" id="max_results" name="max_results">
                                <option value="10">10 items</option>
                                <option value="20" selected>20 items</option>
                                <option value="30">30 items</option>
                                <option value="50">50 items</option>
                            </select>
                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-dollar-sign me-2"></i>Price Range (Optional)
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="min_price" class="form-label">Min Price ($)</label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="min_price" 
                                                   name="min_price" 
                                                   min="0" 
                                                   step="0.01"
                                                   placeholder="0.00">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="max_price" class="form-label">Max Price ($)</label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="max_price" 
                                                   name="max_price" 
                                                   min="0" 
                                                   step="0.01"
                                                   placeholder="1000.00">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-search me-2"></i>Search Outfits
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Search Suggestions -->
        <div class="mt-4">
            <h5><i class="fas fa-lightbulb me-2"></i>Popular Searches</h5>
            <div class="d-flex flex-wrap gap-2">
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="casual summer outfit">
                    Casual Summer
                </button>
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="business professional attire">
                    Business Professional
                </button>
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="date night dress">
                    Date Night
                </button>
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="cozy winter sweater">
                    Winter Cozy
                </button>
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="workout activewear">
                    Activewear
                </button>
                <button class="btn btn-outline-secondary btn-sm quick-search" data-query="elegant evening gown">
                    Evening Wear
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Searching for Perfect Outfits...</h5>
                <p class="text-muted">We're scanning the web for the best deals and styles</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.search-card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 15px;
}

.form-control-lg {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control-lg:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.quick-search {
    border-radius: 20px;
    transition: all 0.3s ease;
}

.quick-search:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

.page-header p {
    color: #6c757d;
    font-size: 1.1rem;
}
</style>

<script>
// Quick search functionality
document.querySelectorAll('.quick-search').forEach(button => {
    button.addEventListener('click', function() {
        const query = this.getAttribute('data-query');
        document.getElementById('style_query').value = query;
        
        // Highlight the selected button
        document.querySelectorAll('.quick-search').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-secondary');
        });
        this.classList.remove('btn-outline-secondary');
        this.classList.add('btn-primary');
    });
});

// Form submission with loading modal
document.getElementById('searchForm').addEventListener('submit', function(e) {
    const query = document.getElementById('style_query').value.trim();
    if (!query) {
        e.preventDefault();
        alert('Please enter a style description');
        return;
    }
    
    // Show loading modal
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
});

// Auto-focus on the search input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('style_query').focus();
});
</script>
{% endblock %}
