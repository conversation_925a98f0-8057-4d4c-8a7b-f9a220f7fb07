# Docker ignore file for Personal Closet Website
# Exclude unnecessary files from Docker build context

# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
*.log

# Development
*.md
README*
CHANGELOG*
LICENSE*

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Scripts (keep only the Python ones)
run-docker.bat
run-docker.sh
*.bat
*.sh

# Temporary files
*.tmp
*.temp
.cache/

# Documentation
docs/
*.txt
!requirements.txt
