#!/usr/bin/env python3
"""
VPN Server CLI interface
"""

import click
import signal
import sys
import time
from pathlib import Path
from tabulate import tabulate

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from src.server.vpn_server import VPNServer
    from src.utils.logger import setup_logging, get_logger
    from src.utils.config import ConfigManager
except ImportError:
    # Fallback for when running from different directory
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from src.server.vpn_server import VPNServer
    from src.utils.logger import setup_logging, get_logger
    from src.utils.config import ConfigManager

logger = get_logger(__name__)

class ServerCLI:
    """VPN Server CLI management"""
    
    def __init__(self):
        self.server: VPNServer = None
        self.running = False
    
    def start_server(self, config_path: Path, daemon: bool = False) -> None:
        """Start VPN server"""
        try:
            # Setup logging
            setup_logging(level="INFO", log_file=Path("logs/vpn-server.log"))
            
            # Create and start server
            self.server = VPNServer(config_path)
            
            # Setup signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Start server
            self.server.start()
            self.running = True
            
            click.echo(f"✅ VPN Server started successfully")
            click.echo(f"📍 Listening on {self.server.config.server.host}:{self.server.config.server.port}")
            click.echo(f"🔒 Protocol: {self.server.config.server.protocol.upper()}")
            click.echo(f"🌐 Network: {self.server.config.network.subnet}")
            
            if not daemon:
                click.echo("Press Ctrl+C to stop the server...")
                self._run_interactive()
            
        except Exception as e:
            click.echo(f"❌ Failed to start server: {e}", err=True)
            sys.exit(1)
    
    def stop_server(self) -> None:
        """Stop VPN server"""
        if self.server:
            click.echo("🛑 Stopping VPN server...")
            self.server.stop()
            self.running = False
            click.echo("✅ VPN server stopped")
    
    def show_status(self, config_path: Path) -> None:
        """Show server status"""
        try:
            # This is a simplified status check
            # In a real implementation, you'd connect to a running server
            config_manager = ConfigManager(config_path)
            config = config_manager.load_config()
            
            click.echo("📊 VPN Server Status")
            click.echo("=" * 50)
            click.echo(f"Configuration: {config_path}")
            click.echo(f"Host: {config.server.host}")
            click.echo(f"Port: {config.server.port}")
            click.echo(f"Protocol: {config.server.protocol.upper()}")
            click.echo(f"Max Clients: {config.server.max_clients}")
            click.echo(f"Network: {config.network.subnet}")
            click.echo(f"DNS Servers: {', '.join(config.network.dns_servers)}")
            
        except Exception as e:
            click.echo(f"❌ Failed to get status: {e}", err=True)
    
    def list_clients(self) -> None:
        """List connected clients"""
        if not self.server:
            click.echo("❌ Server is not running", err=True)
            return
        
        clients = self.server.client_manager.get_client_list()
        
        if not clients:
            click.echo("📭 No clients connected")
            return
        
        # Prepare table data
        headers = ["Client ID", "Address", "Assigned IP", "Status", "Duration", "Bytes Sent", "Bytes Received"]
        rows = []
        
        for client in clients:
            duration = f"{client['duration']:.1f}s"
            status = "✅ Auth" if client['authenticated'] else "🔄 Connecting"
            
            rows.append([
                client['client_id'][:8] + "...",
                client['address'],
                client['assigned_ip'] or "N/A",
                status,
                duration,
                f"{client['bytes_sent']:,}",
                f"{client['bytes_received']:,}"
            ])
        
        click.echo(f"👥 Connected Clients ({len(clients)})")
        click.echo(tabulate(rows, headers=headers, tablefmt="grid"))
    
    def generate_certificates(self, output_dir: Path) -> None:
        """Generate VPN certificates"""
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            
            click.echo("🔐 Generating VPN certificates...")
            
            # This is a simplified certificate generation
            # In a real implementation, you'd use proper OpenSSL commands
            
            ca_cert = output_dir / "ca.crt"
            ca_key = output_dir / "ca.key"
            server_cert = output_dir / "server.crt"
            server_key = output_dir / "server.key"
            dh_pem = output_dir / "dh.pem"
            
            # Create placeholder certificates
            ca_cert.write_text("# CA Certificate Placeholder\n")
            ca_key.write_text("# CA Private Key Placeholder\n")
            server_cert.write_text("# Server Certificate Placeholder\n")
            server_key.write_text("# Server Private Key Placeholder\n")
            dh_pem.write_text("# Diffie-Hellman Parameters Placeholder\n")
            
            click.echo(f"✅ Certificates generated in {output_dir}")
            click.echo("📝 Note: These are placeholder certificates.")
            click.echo("📝 For production use, generate proper certificates with OpenSSL.")
            
        except Exception as e:
            click.echo(f"❌ Certificate generation failed: {e}", err=True)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        click.echo(f"\n🛑 Received signal {signum}, shutting down...")
        self.stop_server()
        sys.exit(0)
    
    def _run_interactive(self) -> None:
        """Run interactive server loop"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

# CLI Commands
@click.group()
@click.version_option(version="1.0.0")
def cli():
    """VPN Server Management CLI"""
    pass

@cli.command()
@click.option('--config', '-c', type=click.Path(exists=True, path_type=Path), 
              default=Path("config/server.yaml"), help='Configuration file path')
@click.option('--daemon', '-d', is_flag=True, help='Run as daemon')
def start(config, daemon):
    """Start VPN server"""
    server_cli = ServerCLI()
    server_cli.start_server(config, daemon)

@cli.command()
@click.option('--config', '-c', type=click.Path(exists=True, path_type=Path), 
              default=Path("config/server.yaml"), help='Configuration file path')
def status(config):
    """Show server status"""
    server_cli = ServerCLI()
    server_cli.show_status(config)

@cli.command()
def clients():
    """List connected clients"""
    server_cli = ServerCLI()
    server_cli.list_clients()

@cli.command()
@click.option('--output', '-o', type=click.Path(path_type=Path), 
              default=Path("config/certificates"), help='Output directory for certificates')
def generate_certs(output):
    """Generate VPN certificates"""
    server_cli = ServerCLI()
    server_cli.generate_certificates(output)

@cli.command()
@click.option('--config', '-c', type=click.Path(path_type=Path), 
              default=Path("config/server.yaml"), help='Configuration file path')
def init_config(config):
    """Initialize default configuration"""
    try:
        config.parent.mkdir(parents=True, exist_ok=True)
        
        config_manager = ConfigManager(config)
        config_manager.load_config()  # This will create default config
        
        click.echo(f"✅ Default configuration created: {config}")
        
    except Exception as e:
        click.echo(f"❌ Configuration initialization failed: {e}", err=True)

def main():
    """Main entry point"""
    cli()

if __name__ == "__main__":
    main()
