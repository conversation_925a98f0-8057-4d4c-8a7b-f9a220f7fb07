"""
Network Manager for VPN Server
"""

import ipaddress
import subprocess
import platform
from typing import Dict, Optional, Set, List
from threading import Lock

from ..utils.logger import get_logger
from ..utils.config import VPNConfig
from ..utils.protocol import ConfigData
from .client_manager import VPNClient

logger = get_logger(__name__)

class NetworkManager:
    """Manages network configuration and routing for VPN server"""
    
    def __init__(self, config: VPNConfig):
        self.config = config
        self.network = ipaddress.IPv4Network(config.network.subnet)
        self.assigned_ips: Dict[str, str] = {}  # client_id -> ip
        self.ip_pool: Set[str] = set()
        self.lock = Lock()
        self.system = platform.system().lower()
        
        self._initialize_ip_pool()
        logger.info("Network Manager initialized", 
                   subnet=config.network.subnet,
                   available_ips=len(self.ip_pool))
    
    def _initialize_ip_pool(self) -> None:
        """Initialize the IP address pool"""
        # Reserve first IP for gateway, last for broadcast
        # Skip first 10 IPs for server use
        hosts = list(self.network.hosts())
        if len(hosts) > 10:
            self.ip_pool = {str(ip) for ip in hosts[10:]}
        else:
            logger.error("Subnet too small for VPN operation")
    
    def setup(self) -> None:
        """Setup network interfaces and routing"""
        try:
            self._setup_tun_interface()
            self._setup_routing()
            self._setup_nat()
            logger.info("Network setup completed")
        except Exception as e:
            logger.error("Network setup failed", error=str(e))
            raise
    
    def cleanup(self) -> None:
        """Cleanup network configuration"""
        try:
            self._cleanup_nat()
            self._cleanup_routing()
            self._cleanup_tun_interface()
            logger.info("Network cleanup completed")
        except Exception as e:
            logger.error("Network cleanup failed", error=str(e))
    
    def assign_ip(self, client_id: str) -> Optional[str]:
        """Assign an IP address to a client"""
        with self.lock:
            if client_id in self.assigned_ips:
                return self.assigned_ips[client_id]
            
            if not self.ip_pool:
                logger.error("No available IP addresses")
                return None
            
            # Get next available IP
            assigned_ip = self.ip_pool.pop()
            self.assigned_ips[client_id] = assigned_ip
            
            logger.info("IP assigned", client_id=client_id, ip=assigned_ip)
            return assigned_ip
    
    def release_ip(self, client_id: str) -> None:
        """Release an IP address from a client"""
        with self.lock:
            if client_id in self.assigned_ips:
                ip = self.assigned_ips.pop(client_id)
                self.ip_pool.add(ip)
                logger.info("IP released", client_id=client_id, ip=ip)
    
    def get_client_config(self, assigned_ip: str) -> ConfigData:
        """Get network configuration for a client"""
        gateway = str(list(self.network.hosts())[0])  # First host as gateway
        netmask = str(self.network.netmask)
        
        return ConfigData(
            assigned_ip=assigned_ip,
            subnet_mask=netmask,
            gateway=gateway,
            dns_servers=self.config.network.dns_servers,
            routes=self.config.network.routes
        )
    
    def route_packet(self, packet_data: bytes, client: VPNClient) -> None:
        """Route a packet from client to destination"""
        try:
            # Simple packet routing - in a real implementation,
            # this would parse the IP packet and route accordingly
            logger.debug("Routing packet", 
                        client=client.client_id, 
                        size=len(packet_data))
            
            # Update client statistics
            # This is where you'd implement actual packet forwarding
            
        except Exception as e:
            logger.error("Packet routing failed", error=str(e))
    
    def _setup_tun_interface(self) -> None:
        """Setup TUN interface"""
        if self.system == "windows":
            self._setup_tun_windows()
        elif self.system == "linux":
            self._setup_tun_linux()
        elif self.system == "darwin":
            self._setup_tun_macos()
        else:
            logger.warning("Unsupported operating system", system=self.system)
    
    def _setup_tun_windows(self) -> None:
        """Setup TUN interface on Windows"""
        try:
            # Windows TUN setup using TAP-Windows adapter
            # This is a simplified version - real implementation would use
            # Windows TAP driver or WinTUN
            logger.info("Setting up Windows TUN interface")
            
            # Commands would be something like:
            # netsh interface ip set address "TAP-Windows Adapter" static <gateway_ip> <netmask>
            gateway = str(list(self.network.hosts())[0])
            netmask = str(self.network.netmask)
            
            # Note: This requires TAP-Windows driver to be installed
            logger.info("Windows TUN interface configured", gateway=gateway, netmask=netmask)
            
        except Exception as e:
            logger.error("Windows TUN setup failed", error=str(e))
    
    def _setup_tun_linux(self) -> None:
        """Setup TUN interface on Linux"""
        try:
            gateway = str(list(self.network.hosts())[0])
            
            # Create TUN interface
            subprocess.run([
                "ip", "tuntap", "add", "dev", "vpn0", "mode", "tun"
            ], check=True)
            
            # Configure interface
            subprocess.run([
                "ip", "addr", "add", f"{gateway}/{self.network.prefixlen}", 
                "dev", "vpn0"
            ], check=True)
            
            # Bring interface up
            subprocess.run(["ip", "link", "set", "vpn0", "up"], check=True)
            
            logger.info("Linux TUN interface created", interface="vpn0", gateway=gateway)
            
        except subprocess.CalledProcessError as e:
            logger.error("Linux TUN setup failed", error=str(e))
    
    def _setup_tun_macos(self) -> None:
        """Setup TUN interface on macOS"""
        try:
            gateway = str(list(self.network.hosts())[0])
            
            # macOS TUN setup (requires tuntaposx or similar)
            subprocess.run([
                "ifconfig", "tun0", "create"
            ], check=True)
            
            subprocess.run([
                "ifconfig", "tun0", gateway, "netmask", str(self.network.netmask)
            ], check=True)
            
            logger.info("macOS TUN interface created", interface="tun0", gateway=gateway)
            
        except subprocess.CalledProcessError as e:
            logger.error("macOS TUN setup failed", error=str(e))
    
    def _setup_routing(self) -> None:
        """Setup routing rules"""
        try:
            if self.system == "linux":
                # Enable IP forwarding
                subprocess.run([
                    "sysctl", "-w", "net.ipv4.ip_forward=1"
                ], check=True)
                
            elif self.system == "windows":
                # Windows routing setup
                pass
                
            logger.info("Routing configured")
            
        except subprocess.CalledProcessError as e:
            logger.error("Routing setup failed", error=str(e))
    
    def _setup_nat(self) -> None:
        """Setup NAT rules"""
        try:
            if self.system == "linux":
                # Setup iptables NAT rules
                subprocess.run([
                    "iptables", "-t", "nat", "-A", "POSTROUTING", 
                    "-s", str(self.network), "-j", "MASQUERADE"
                ], check=True)
                
                subprocess.run([
                    "iptables", "-A", "FORWARD", "-i", "vpn0", "-j", "ACCEPT"
                ], check=True)
                
                subprocess.run([
                    "iptables", "-A", "FORWARD", "-o", "vpn0", "-j", "ACCEPT"
                ], check=True)
                
            logger.info("NAT rules configured")
            
        except subprocess.CalledProcessError as e:
            logger.error("NAT setup failed", error=str(e))
    
    def _cleanup_tun_interface(self) -> None:
        """Cleanup TUN interface"""
        try:
            if self.system == "linux":
                subprocess.run(["ip", "link", "delete", "vpn0"], check=False)
            elif self.system == "darwin":
                subprocess.run(["ifconfig", "tun0", "destroy"], check=False)
            
            logger.info("TUN interface cleaned up")
            
        except Exception as e:
            logger.error("TUN cleanup failed", error=str(e))
    
    def _cleanup_routing(self) -> None:
        """Cleanup routing rules"""
        try:
            if self.system == "linux":
                subprocess.run([
                    "sysctl", "-w", "net.ipv4.ip_forward=0"
                ], check=False)
            
            logger.info("Routing cleaned up")
            
        except Exception as e:
            logger.error("Routing cleanup failed", error=str(e))
    
    def _cleanup_nat(self) -> None:
        """Cleanup NAT rules"""
        try:
            if self.system == "linux":
                subprocess.run([
                    "iptables", "-t", "nat", "-D", "POSTROUTING", 
                    "-s", str(self.network), "-j", "MASQUERADE"
                ], check=False)
                
                subprocess.run([
                    "iptables", "-D", "FORWARD", "-i", "vpn0", "-j", "ACCEPT"
                ], check=False)
                
                subprocess.run([
                    "iptables", "-D", "FORWARD", "-o", "vpn0", "-j", "ACCEPT"
                ], check=False)
            
            logger.info("NAT rules cleaned up")
            
        except Exception as e:
            logger.error("NAT cleanup failed", error=str(e))
