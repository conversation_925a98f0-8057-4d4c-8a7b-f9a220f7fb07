# Personal Closet Web Application - Python Dependencies
Flask==2.3.3
Werkzeug==2.3.7

# The web application requires Python 3.7+ and uses:
# - Flask for web framework
# - Werkzeug for file uploads and utilities
# - Built-in modules: dataclasses, enum, json, uuid, datetime
# - typing (built-in)
# - datetime (built-in)
# - random (built-in)
# - json (built-in)
# - sys (built-in)

# Web scraping and API dependencies:
requests>=2.25.0         # For web scraping and API calls
beautifulsoup4>=4.12.0   # For HTML parsing
lxml>=4.9.0             # For XML/HTML parsing
urllib3>=1.26.0         # For HTTP requests

# Optional dependencies for future enhancements:
# colorama>=0.4.4         # For colored terminal output
# rich>=10.0.0           # For enhanced CLI formatting
# pillow>=8.0.0          # For future image processing
# numpy>=1.20.0          # For advanced color analysis
