#!/usr/bin/env python3
"""
VPN Demo Script

This script demonstrates the VPN functionality with a simple server-client setup.
"""

import sys
import time
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.server.vpn_server import VPNServer
from src.client.vpn_client import VP<PERSON>lient
from src.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)

class VPNDemo:
    """VPN demonstration class"""
    
    def __init__(self):
        self.server = None
        self.client = None
        self.server_thread = None
        
    def run_demo(self):
        """Run the VPN demonstration"""
        print("🚀 Starting VPN Demo")
        print("=" * 50)
        
        try:
            # Setup logging
            setup_logging(level="INFO")
            
            # Start server
            print("1️⃣  Starting VPN Server...")
            self.start_server()
            
            # Wait a moment for server to start
            time.sleep(2)
            
            # Start client
            print("2️⃣  Starting VPN Client...")
            self.start_client()
            
            # Run demo
            print("3️⃣  Running demo...")
            self.run_demo_scenario()
            
        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted by user")
        except Exception as e:
            print(f"❌ Demo failed: {e}")
        finally:
            self.cleanup()
    
    def start_server(self):
        """Start the VPN server"""
        try:
            config_path = Path("config/server.yaml")
            self.server = VPNServer(config_path)
            
            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.server.start, daemon=True)
            self.server_thread.start()
            
            print("✅ VPN Server started on 127.0.0.1:1194")
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            raise
    
    def start_client(self):
        """Start the VPN client"""
        try:
            config_path = Path("config/client.yaml")
            self.client = VPNClient(config_path)
            
            # Setup callbacks
            self.client.on_connected = lambda: print("✅ Client connected to server")
            self.client.on_disconnected = lambda: print("📡 Client disconnected from server")
            
            # Connect to server
            if self.client.connect("127.0.0.1", 1194):
                print("✅ VPN Client connected successfully")
            else:
                print("❌ VPN Client failed to connect")
                raise Exception("Client connection failed")
                
        except Exception as e:
            print(f"❌ Failed to start client: {e}")
            raise
    
    def run_demo_scenario(self):
        """Run demonstration scenario"""
        print("\n📊 Demo Scenario Running...")
        print("-" * 30)
        
        # Show server status
        if self.server:
            status = self.server.get_status()
            print(f"🖥️  Server Status: {'Running' if status['running'] else 'Stopped'}")
            print(f"👥 Connected Clients: {status['clients']}")
        
        # Show client status
        if self.client:
            status = self.client.get_status()
            print(f"📱 Client Status: {'Connected' if status['connected'] else 'Disconnected'}")
            print(f"🔐 Authenticated: {'Yes' if status['authenticated'] else 'No'}")
            print(f"🌐 Assigned IP: {status['assigned_ip'] or 'N/A'}")
        
        # Simulate some activity
        print("\n🔄 Simulating VPN activity...")
        for i in range(5):
            print(f"   📦 Sending test packet {i+1}/5...")
            
            # Send test data through VPN
            if self.client and self.client.authenticated:
                test_data = f"Test packet {i+1}".encode()
                self.client.send_data(test_data)
            
            time.sleep(1)
        
        print("✅ Demo scenario completed")
        
        # Keep running for a bit
        print("\n⏱️  Demo will run for 10 more seconds...")
        print("   Press Ctrl+C to stop early")
        
        try:
            time.sleep(10)
        except KeyboardInterrupt:
            pass
    
    def cleanup(self):
        """Cleanup resources"""
        print("\n🧹 Cleaning up...")
        
        if self.client:
            self.client.disconnect()
            print("✅ Client disconnected")
        
        if self.server:
            self.server.stop()
            print("✅ Server stopped")
        
        print("✅ Demo cleanup completed")

def main():
    """Main entry point"""
    print("🔒 Python VPN Implementation Demo")
    print("==================================")
    print()
    print("This demo will:")
    print("• Start a VPN server on localhost:1194")
    print("• Connect a VPN client to the server")
    print("• Demonstrate basic VPN functionality")
    print("• Show connection status and statistics")
    print()
    
    try:
        input("Press Enter to start the demo (Ctrl+C to cancel)...")
    except KeyboardInterrupt:
        print("\n❌ Demo cancelled")
        return
    
    demo = VPNDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
