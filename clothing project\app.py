#!/usr/bin/env python3
"""
Personal Closet Website - Flask Application
A web-based personal wardrobe manager and outfit generator
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
from personal_closet import PersonalCloset, PersonalClothingItem
from clothing_generator import Occasion, Weather, Style
from outfit_search import OutfitSearchEngine

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize personal closet and search engine
closet = PersonalCloset()
search_engine = OutfitSearchEngine()

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """Home page with closet overview"""
    stats = closet.get_closet_stats()
    recent_outfits = closet.get_recent_outfits(limit=3)
    return render_template('index.html', stats=stats, recent_outfits=recent_outfits)


@app.route('/closet')
def view_closet():
    """View all clothing items in closet"""
    category = request.args.get('category', 'all')
    items = closet.get_items_by_category(category) if category != 'all' else closet.get_all_items()
    categories = closet.get_categories()
    return render_template('closet.html', items=items, categories=categories, current_category=category)


@app.route('/add-item', methods=['GET', 'POST'])
def add_item():
    """Add new clothing item to closet"""
    if request.method == 'POST':
        try:
            # Handle file upload
            photo_filename = None
            if 'photo' in request.files:
                file = request.files['photo']
                if file and file.filename != '' and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    # Add timestamp to avoid conflicts
                    name, ext = os.path.splitext(filename)
                    photo_filename = f"{name}_{int(datetime.now().timestamp())}{ext}"
                    file.save(os.path.join(app.config['UPLOAD_FOLDER'], photo_filename))
            
            # Create clothing item
            item_data = {
                'id': '',  # Will be auto-generated by __post_init__
                'name': request.form['name'],
                'category': request.form['category'],
                'colors': [color.strip() for color in request.form['colors'].split(',')],
                'brand': request.form.get('brand', ''),
                'size': request.form.get('size', ''),
                'material': request.form.get('material', ''),
                'purchase_date': request.form.get('purchase_date', ''),
                'cost': float(request.form.get('cost', 0)) if request.form.get('cost') else 0,
                'photo_filename': photo_filename,
                'style': [Style(style) for style in request.form.getlist('styles')],
                'occasions': [Occasion(occasion) for occasion in request.form.getlist('occasions')],
                'weather': [Weather(weather) for weather in request.form.getlist('weather')],
                'formality': int(request.form.get('formality', 5)),
                'versatility': int(request.form.get('versatility', 5)),
                'condition': request.form.get('condition', 'good'),
                'notes': request.form.get('notes', '')
            }
            
            item = PersonalClothingItem(**item_data)
            closet.add_item(item)
            flash('Item added successfully!', 'success')
            return redirect(url_for('view_closet'))
            
        except Exception as e:
            flash(f'Error adding item: {str(e)}', 'error')
    
    return render_template('add_item.html', 
                         styles=list(Style), 
                         occasions=list(Occasion), 
                         weather_options=list(Weather))


@app.route('/item/<item_id>')
def view_item(item_id):
    """View detailed information about a clothing item"""
    item = closet.get_item_by_id(item_id)
    if not item:
        flash('Item not found', 'error')
        return redirect(url_for('view_closet'))
    
    outfit_history = closet.get_outfits_with_item(item_id)
    return render_template('item_detail.html', item=item, outfit_history=outfit_history)


@app.route('/edit-item/<item_id>', methods=['GET', 'POST'])
def edit_item(item_id):
    """Edit clothing item"""
    item = closet.get_item_by_id(item_id)
    if not item:
        flash('Item not found', 'error')
        return redirect(url_for('view_closet'))
    
    if request.method == 'POST':
        try:
            # Handle photo upload
            if 'photo' in request.files:
                file = request.files['photo']
                if file and file.filename != '' and allowed_file(file.filename):
                    # Delete old photo if exists
                    if item.photo_filename:
                        old_path = os.path.join(app.config['UPLOAD_FOLDER'], item.photo_filename)
                        if os.path.exists(old_path):
                            os.remove(old_path)
                    
                    filename = secure_filename(file.filename)
                    name, ext = os.path.splitext(filename)
                    photo_filename = f"{name}_{int(datetime.now().timestamp())}{ext}"
                    file.save(os.path.join(app.config['UPLOAD_FOLDER'], photo_filename))
                    item.photo_filename = photo_filename
            
            # Update item data
            item.name = request.form['name']
            item.category = request.form['category']
            item.colors = [color.strip() for color in request.form['colors'].split(',')]
            item.brand = request.form.get('brand', '')
            item.size = request.form.get('size', '')
            item.material = request.form.get('material', '')
            item.purchase_date = request.form.get('purchase_date', '')
            item.cost = float(request.form.get('cost', 0)) if request.form.get('cost') else 0
            item.style = [Style(style) for style in request.form.getlist('styles')]
            item.occasions = [Occasion(occasion) for occasion in request.form.getlist('occasions')]
            item.weather = [Weather(weather) for weather in request.form.getlist('weather')]
            item.formality = int(request.form.get('formality', 5))
            item.versatility = int(request.form.get('versatility', 5))
            item.condition = request.form.get('condition', 'good')
            item.notes = request.form.get('notes', '')
            
            closet.update_item(item)
            flash('Item updated successfully!', 'success')
            return redirect(url_for('view_item', item_id=item_id))
            
        except Exception as e:
            flash(f'Error updating item: {str(e)}', 'error')
    
    return render_template('edit_item.html', 
                         item=item,
                         styles=list(Style), 
                         occasions=list(Occasion), 
                         weather_options=list(Weather))


@app.route('/delete-item/<item_id>', methods=['GET', 'POST'])
def delete_item(item_id):
    """Delete clothing item"""
    item = closet.get_item_by_id(item_id)
    if item:
        # Delete photo file if exists
        if item.photo_filename:
            photo_path = os.path.join(app.config['UPLOAD_FOLDER'], item.photo_filename)
            if os.path.exists(photo_path):
                os.remove(photo_path)
        
        closet.remove_item(item_id)
        flash('Item deleted successfully!', 'success')
    else:
        flash('Item not found', 'error')
    
    return redirect(url_for('view_closet'))


@app.route('/generate-outfit', methods=['GET', 'POST'])
def generate_outfit():
    """Generate outfit from personal closet"""
    if request.method == 'POST':
        try:
            occasion = Occasion(request.form['occasion'])
            weather = Weather(request.form['weather'])
            preferred_style = Style(request.form['style']) if request.form.get('style') else None
            color_preference = request.form.get('color_preference') if request.form.get('color_preference') else None
            generation_mode = request.form.get('generation_mode', 'auto')

            if generation_mode == 'custom':
                # Custom outfit generation with specific item types
                custom_items = request.form.getlist('custom_items')
                if not custom_items:
                    flash('Please select at least one item type for custom outfit generation', 'error')
                    return render_template('generate_outfit.html',
                                         styles=list(Style),
                                         occasions=list(Occasion),
                                         weather_options=list(Weather))

                outfits = closet.generate_custom_outfits(
                    occasion=occasion,
                    weather=weather,
                    preferred_style=preferred_style,
                    color_preference=color_preference,
                    custom_items=custom_items,
                    num_suggestions=3
                )
            else:
                # Auto generation (existing functionality)
                outfits = closet.generate_personal_outfits(
                    occasion=occasion,
                    weather=weather,
                    preferred_style=preferred_style,
                    color_preference=color_preference,
                    num_suggestions=3
                )

            return render_template('outfit_results.html',
                                 outfits=outfits,
                                 occasion=occasion,
                                 weather=weather,
                                 preferred_style=preferred_style,
                                 generation_mode=generation_mode,
                                 custom_items=custom_items if generation_mode == 'custom' else None)

        except Exception as e:
            flash(f'Error generating outfit: {str(e)}', 'error')

    return render_template('generate_outfit.html',
                         styles=list(Style),
                         occasions=list(Occasion),
                         weather_options=list(Weather))


@app.route('/save-outfit', methods=['POST'])
def save_outfit():
    """Save generated outfit to history"""
    try:
        outfit_data = request.get_json()
        outfit_id = closet.save_outfit_to_history(outfit_data)
        return jsonify({'success': True, 'outfit_id': outfit_id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/outfit-history')
def outfit_history():
    """View outfit history"""
    outfits = closet.get_outfit_history()
    return render_template('outfit_history.html', outfits=outfits)


@app.route('/favorites')
def favorites():
    """View favorite outfits"""
    favorites = closet.get_favorite_outfits()
    return render_template('favorites.html', favorites=favorites)


@app.route('/toggle-favorite/<outfit_id>', methods=['POST'])
def toggle_favorite(outfit_id):
    """Toggle outfit favorite status"""
    try:
        is_favorite = closet.toggle_outfit_favorite(outfit_id)
        return jsonify({'success': True, 'is_favorite': is_favorite})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/toggle-item-favorite/<item_id>', methods=['POST'])
def toggle_item_favorite(item_id):
    """Toggle item favorite status"""
    try:
        is_favorite = closet.toggle_item_favorite(item_id)
        return jsonify({'success': True, 'is_favorite': is_favorite})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/closet-stats')
def api_closet_stats():
    """API endpoint for closet statistics"""
    stats = closet.get_closet_stats()

    # Convert PersonalClothingItem objects to dictionaries for JSON serialization
    if stats.get('most_worn'):
        stats['most_worn'] = {
            'name': stats['most_worn'].name,
            'category': stats['most_worn'].category,
            'wear_count': stats['most_worn'].wear_count
        }

    if stats.get('least_worn'):
        stats['least_worn'] = {
            'name': stats['least_worn'].name,
            'category': stats['least_worn'].category,
            'wear_count': stats['least_worn'].wear_count
        }

    if stats.get('recent_additions'):
        stats['recent_additions'] = [
            {
                'name': item.name,
                'category': item.category,
                'date_added': item.date_added.isoformat() if item.date_added else None
            }
            for item in stats['recent_additions']
        ]

    return jsonify(stats)


@app.route('/api/favorite-items')
def api_favorite_items():
    """API endpoint for favorite items"""
    try:
        favorite_items = closet.get_favorite_items()
        items_data = []
        for item in favorite_items:
            items_data.append({
                'id': item.id,
                'name': item.name,
                'brand': item.brand,
                'photo_filename': item.photo_filename,
                'category': item.category,
                'colors': item.colors
            })
        return jsonify({'success': True, 'items': items_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/outfit-suggestions')
def api_outfit_suggestions():
    """API endpoint for quick outfit suggestions"""
    try:
        occasion = Occasion(request.args.get('occasion', 'casual'))
        weather = Weather(request.args.get('weather', 'mild'))

        outfits = closet.generate_personal_outfits(
            occasion=occasion,
            weather=weather,
            num_suggestions=1
        )

        if outfits:
            outfit = outfits[0]
            return jsonify({
                'success': True,
                'outfit': {
                    'items': [{'name': item.name, 'photo': item.photo_filename} for item in outfit.items],
                    'score': outfit.overall_score,
                    'description': outfit.description
                }
            })
        else:
            return jsonify({'success': False, 'message': 'No suitable outfits found'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/search-outfits', methods=['GET', 'POST'])
def search_outfits():
    """Search for outfits online with pricing"""
    # Handle GET request with query parameters (for quick search)
    if request.method == 'GET' and request.args.get('style_query'):
        try:
            style_query = request.args.get('style_query', '').strip()
            occasion = request.args.get('occasion', 'casual')
            gender = request.args.get('gender', 'unisex')
            max_results = int(request.args.get('max_results', 20))

            if style_query:
                # Perform search
                search_results = search_engine.search_outfits(
                    style_query=style_query,
                    occasion=occasion,
                    gender=gender,
                    max_results=max_results
                )

                return render_template('search_results.html',
                                     results=search_results,
                                     query=style_query,
                                     occasion=occasion,
                                     gender=gender,
                                     total_results=len(search_results))
        except Exception as e:
            flash(f'Search error: {str(e)}', 'error')

    # Handle POST request (form submission)
    if request.method == 'POST':
        try:
            style_query = request.form.get('style_query', '').strip()
            occasion = request.form.get('occasion', 'casual')
            gender = request.form.get('gender', 'unisex')
            max_results = int(request.form.get('max_results', 20))

            # Parse price range if provided
            price_range = None
            min_price = request.form.get('min_price')
            max_price = request.form.get('max_price')
            if min_price and max_price:
                try:
                    price_range = (float(min_price), float(max_price))
                except ValueError:
                    pass

            if not style_query:
                flash('Please enter a style description', 'error')
                return redirect(url_for('search_outfits'))

            # Perform search
            search_results = search_engine.search_outfits(
                style_query=style_query,
                occasion=occasion,
                gender=gender,
                max_results=max_results,
                price_range=price_range
            )

            return render_template('search_results.html',
                                 results=search_results,
                                 query=style_query,
                                 occasion=occasion,
                                 gender=gender,
                                 total_results=len(search_results))

        except Exception as e:
            flash(f'Search error: {str(e)}', 'error')

    return render_template('search_outfits.html',
                         occasions=list(Occasion),
                         styles=list(Style))


@app.route('/api/search-outfits')
def api_search_outfits():
    """API endpoint for outfit search"""
    try:
        style_query = request.args.get('query', '').strip()
        occasion = request.args.get('occasion', 'casual')
        gender = request.args.get('gender', 'unisex')
        max_results = int(request.args.get('max_results', 10))

        if not style_query:
            return jsonify({'success': False, 'error': 'Query parameter required'})

        # Perform search
        search_results = search_engine.search_outfits(
            style_query=style_query,
            occasion=occasion,
            gender=gender,
            max_results=max_results
        )

        # Convert results to JSON-serializable format
        results_data = []
        for result in search_results:
            results_data.append({
                'title': result.title,
                'price': result.price,
                'original_price': result.original_price,
                'discount': result.discount,
                'image_url': result.image_url,
                'product_url': result.product_url,
                'store': result.store,
                'rating': result.rating,
                'reviews_count': result.reviews_count,
                'description': result.description,
                'category': result.category
            })

        return jsonify({
            'success': True,
            'results': results_data,
            'total_results': len(results_data),
            'query': style_query
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


if __name__ == '__main__':
    # Docker-friendly configuration
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    app.run(debug=debug_mode, host='0.0.0.0', port=5000)
