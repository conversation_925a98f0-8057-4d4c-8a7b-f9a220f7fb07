{% extends "base.html" %}

{% block title %}Add Item - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-plus-circle me-3"></i>Add New Item</h1>
    <p>Add a new clothing item to your personal wardrobe</p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="addItemForm">
                    <!-- Photo Upload -->
                    <div class="mb-4">
                        <label for="photo" class="form-label">
                            <i class="fas fa-camera me-2"></i>Item Photo
                        </label>
                        <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                            <div id="photoPreview" class="photo-preview">
                                <i class="fas fa-camera fa-3x text-muted mb-2"></i>
                                <p class="text-muted">Click to upload photo</p>
                                <small class="text-muted">Supports: JPG, PNG, GIF, WebP (Max 16MB)</small>
                            </div>
                        </div>
                        <input type="file" class="form-control d-none" id="photo" name="photo" 
                               accept="image/*" onchange="previewPhoto(this)">
                    </div>

                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="name" class="form-label">Item Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   placeholder="e.g., Blue Denim Jacket">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="top">Top</option>
                                <option value="bottom">Bottom</option>
                                <option value="dress">Dress</option>
                                <option value="outerwear">Outerwear</option>
                                <option value="shoes">Shoes</option>
                                <option value="accessory">Accessory</option>
                            </select>
                        </div>
                    </div>

                    <!-- Colors -->
                    <div class="mb-4">
                        <label for="colors" class="form-label">
                            <i class="fas fa-palette me-2"></i>Colors *
                        </label>

                        <!-- Color Picker Wheel -->
                        <div class="color-picker-section mb-3">
                            <div class="color-wheel">
                                <div class="color-wheel-label mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-mouse-pointer me-1"></i>
                                        Click colors to add them:
                                    </small>
                                </div>
                                <div class="color-grid">
                                    <!-- Basic Colors Row 1 -->
                                    <div class="color-option" data-color="black" style="background-color: #000000" title="Black"></div>
                                    <div class="color-option" data-color="white" style="background-color: #ffffff" title="White"></div>
                                    <div class="color-option" data-color="gray" style="background-color: #808080" title="Gray"></div>
                                    <div class="color-option" data-color="red" style="background-color: #dc3545" title="Red"></div>
                                    <div class="color-option" data-color="blue" style="background-color: #007bff" title="Blue"></div>
                                    <div class="color-option" data-color="green" style="background-color: #28a745" title="Green"></div>
                                    <div class="color-option" data-color="yellow" style="background-color: #ffc107" title="Yellow"></div>
                                    <div class="color-option" data-color="orange" style="background-color: #fd7e14" title="Orange"></div>

                                    <!-- Basic Colors Row 2 -->
                                    <div class="color-option" data-color="purple" style="background-color: #6f42c1" title="Purple"></div>
                                    <div class="color-option" data-color="pink" style="background-color: #e83e8c" title="Pink"></div>
                                    <div class="color-option" data-color="brown" style="background-color: #8b4513" title="Brown"></div>
                                    <div class="color-option" data-color="beige" style="background-color: #f5f5dc" title="Beige"></div>
                                    <div class="color-option" data-color="navy" style="background-color: #000080" title="Navy"></div>
                                    <div class="color-option" data-color="maroon" style="background-color: #800000" title="Maroon"></div>
                                    <div class="color-option" data-color="olive" style="background-color: #808000" title="Olive"></div>
                                    <div class="color-option" data-color="teal" style="background-color: #008080" title="Teal"></div>

                                    <!-- Extended Colors Row 3 -->
                                    <div class="color-option" data-color="cream" style="background-color: #fffdd0" title="Cream"></div>
                                    <div class="color-option" data-color="tan" style="background-color: #d2b48c" title="Tan"></div>
                                    <div class="color-option" data-color="khaki" style="background-color: #f0e68c" title="Khaki"></div>
                                    <div class="color-option" data-color="coral" style="background-color: #ff7f50" title="Coral"></div>
                                    <div class="color-option" data-color="salmon" style="background-color: #fa8072" title="Salmon"></div>
                                    <div class="color-option" data-color="lavender" style="background-color: #e6e6fa" title="Lavender"></div>
                                    <div class="color-option" data-color="mint" style="background-color: #98fb98" title="Mint"></div>
                                    <div class="color-option" data-color="peach" style="background-color: #ffcba4" title="Peach"></div>
                                </div>
                            </div>

                            <!-- Selected Colors Display -->
                            <div class="selected-colors mt-3">
                                <div class="selected-colors-label mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Selected colors:
                                    </small>
                                </div>
                                <div class="selected-colors-display" id="selectedColorsDisplay">
                                    <span class="text-muted">No colors selected</span>
                                </div>
                            </div>
                        </div>

                        <!-- Text Input (still available for custom colors) -->
                        <input type="text" class="form-control" id="colors" name="colors" required
                               placeholder="Selected colors will appear here, or type custom colors">
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Colors are automatically added from the picker above, or you can type custom colors separated by commas
                        </div>
                    </div>

                    <!-- Brand and Details -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="brand" class="form-label">Brand</label>
                            <input type="text" class="form-control" id="brand" name="brand" 
                                   placeholder="e.g., Levi's">
                        </div>
                        <div class="col-md-4">
                            <label for="size" class="form-label">Size</label>
                            <input type="text" class="form-control" id="size" name="size" 
                                   placeholder="e.g., M, 32, 8.5">
                        </div>
                        <div class="col-md-4">
                            <label for="material" class="form-label">Material</label>
                            <input type="text" class="form-control" id="material" name="material" 
                                   placeholder="e.g., Cotton, Denim">
                        </div>
                    </div>

                    <!-- Purchase Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                        </div>
                        <div class="col-md-6">
                            <label for="cost" class="form-label">Cost ($)</label>
                            <input type="number" class="form-control" id="cost" name="cost" 
                                   step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>

                    <!-- Style Categories -->
                    <div class="mb-3">
                        <label class="form-label">Styles *</label>
                        <div class="form-text mb-2">Select all styles that apply to this item</div>
                        <div class="row">
                            {% for style in styles %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="style_{{ style.value }}" name="styles" value="{{ style.value }}">
                                    <label class="form-check-label" for="style_{{ style.value }}">
                                        {{ style.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Occasions -->
                    <div class="mb-3">
                        <label class="form-label">Suitable Occasions *</label>
                        <div class="form-text mb-2">Select occasions where this item would be appropriate</div>
                        <div class="row">
                            {% for occasion in occasions %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="occasion_{{ occasion.value }}" name="occasions" value="{{ occasion.value }}">
                                    <label class="form-check-label" for="occasion_{{ occasion.value }}">
                                        {{ occasion.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Exclusive Occasions (Optional) -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-lock me-2"></i>Exclusive Occasions (Optional)
                        </label>
                        <div class="form-text mb-2">
                            <strong>Select ONLY if this item should be restricted to specific occasions.</strong><br>
                            If selected, this item will NEVER be paired with items from other occasions.<br>
                            Leave empty for normal occasion flexibility.
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Example:</strong> A formal tuxedo should only be for "Formal" occasions,
                            so it won't be mixed with casual items.
                        </div>
                        <div class="row">
                            {% for occasion in occasions %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="exclusive_occasion_{{ occasion.value }}" name="exclusive_occasions" value="{{ occasion.value }}">
                                    <label class="form-check-label" for="exclusive_occasion_{{ occasion.value }}">
                                        <i class="fas fa-lock me-1"></i>{{ occasion.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label class="form-label">Weather Conditions *</label>
                        <div class="form-text mb-2">Select weather conditions suitable for this item</div>
                        <div class="row">
                            {% for weather in weather_options %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="weather_{{ weather.value }}" name="weather" value="{{ weather.value }}">
                                    <label class="form-check-label" for="weather_{{ weather.value }}">
                                        {{ weather.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Ratings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="formality" class="form-label">Formality Level</label>
                            <input type="range" class="form-range" id="formality" name="formality" 
                                   min="1" max="10" value="5" oninput="updateRangeValue('formality')">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Casual</small>
                                <small class="text-muted">Value: <span id="formalityValue">5</span></small>
                                <small class="text-muted">Formal</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="versatility" class="form-label">Versatility</label>
                            <input type="range" class="form-range" id="versatility" name="versatility" 
                                   min="1" max="10" value="5" oninput="updateRangeValue('versatility')">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Specific</small>
                                <small class="text-muted">Value: <span id="versatilityValue">5</span></small>
                                <small class="text-muted">Versatile</small>
                            </div>
                        </div>
                    </div>

                    <!-- Condition -->
                    <div class="mb-3">
                        <label for="condition" class="form-label">Condition</label>
                        <select class="form-select" id="condition" name="condition">
                            <option value="excellent">Excellent</option>
                            <option value="good" selected>Good</option>
                            <option value="fair">Fair</option>
                            <option value="poor">Poor</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Any additional notes about this item..."></textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Add Item
                        </button>
                        <a href="{{ url_for('view_closet') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.photo-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.photo-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(108, 92, 231, 0.05);
}

.photo-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 10px;
    object-fit: cover;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Color Picker Styles */
.color-picker-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
}

.color-wheel-label {
    text-align: center;
    margin-bottom: 1rem;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.75rem;
    justify-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.color-option {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    z-index: 1;
}

.color-option.selected {
    transform: scale(1.1);
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.color-option[style*="ffffff"]::after {
    color: #333;
    text-shadow: none;
}

.color-option[style*="ffffff"] {
    border-color: #dee2e6;
}

.color-option[style*="000000"] {
    border-color: #6c757d;
}

.selected-colors {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.selected-colors-display {
    min-height: 40px;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.selected-color-tag {
    display: inline-flex;
    align-items: center;
    background: #e9ecef;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
    color: #495057;
    border: 1px solid #ced4da;
}

.selected-color-tag .color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.selected-color-tag .remove-color {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #6c757d;
    font-weight: bold;
}

.selected-color-tag .remove-color:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 0.5rem;
    }

    .color-option {
        width: 32px;
        height: 32px;
    }

    .color-picker-section {
        padding: 1rem;
    }
}

.form-range::-webkit-slider-thumb {
    background: var(--primary-color);
}

.form-range::-moz-range-thumb {
    background: var(--primary-color);
    border: none;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewPhoto(input) {
    const preview = document.getElementById('photoPreview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" style="max-width: 100%; max-height: 200px; border-radius: 10px; object-fit: cover;">
                <p class="text-muted mt-2">Click to change photo</p>
            `;
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

function updateRangeValue(rangeName) {
    const range = document.getElementById(rangeName);
    const valueSpan = document.getElementById(rangeName + 'Value');
    valueSpan.textContent = range.value;
}

// Form validation
document.getElementById('addItemForm').addEventListener('submit', function(e) {
    // Check if at least one style is selected
    const styles = document.querySelectorAll('input[name="styles"]:checked');
    if (styles.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one style for this item.', 'error');
        return;
    }
    
    // Check if at least one occasion is selected
    const occasions = document.querySelectorAll('input[name="occasions"]:checked');
    if (occasions.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one occasion for this item.', 'error');
        return;
    }
    
    // Check if at least one weather condition is selected
    const weather = document.querySelectorAll('input[name="weather"]:checked');
    if (weather.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one weather condition for this item.', 'error');
        return;
    }
});

// Auto-suggest based on category
document.getElementById('category').addEventListener('change', function() {
    const category = this.value;
    const suggestions = {
        'top': {
            styles: ['casual', 'business', 'classic'],
            occasions: ['casual', 'work', 'social'],
            weather: ['mild', 'warm']
        },
        'bottom': {
            styles: ['casual', 'business', 'classic'],
            occasions: ['casual', 'work', 'social'],
            weather: ['mild', 'warm', 'cool']
        },
        'dress': {
            styles: ['romantic', 'classic', 'trendy'],
            occasions: ['formal', 'social', 'date'],
            weather: ['warm', 'mild']
        },
        'outerwear': {
            styles: ['classic', 'casual', 'edgy'],
            occasions: ['casual', 'work', 'outdoor'],
            weather: ['cool', 'cold']
        },
        'shoes': {
            styles: ['classic', 'casual', 'sporty'],
            occasions: ['casual', 'work', 'exercise'],
            weather: ['mild', 'warm', 'cool']
        },
        'accessory': {
            styles: ['classic', 'trendy', 'minimalist'],
            occasions: ['casual', 'formal', 'social'],
            weather: ['mild', 'warm', 'cool', 'cold']
        }
    };
    
    if (suggestions[category]) {
        // Auto-check suggested options
        const suggestion = suggestions[category];
        
        // Clear all checkboxes first
        document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
        
        // Check suggested styles
        suggestion.styles.forEach(style => {
            const checkbox = document.getElementById(`style_${style}`);
            if (checkbox) checkbox.checked = true;
        });
        
        // Check suggested occasions
        suggestion.occasions.forEach(occasion => {
            const checkbox = document.getElementById(`occasion_${occasion}`);
            if (checkbox) checkbox.checked = true;
        });
        
        // Check suggested weather
        suggestion.weather.forEach(weather => {
            const checkbox = document.getElementById(`weather_${weather}`);
            if (checkbox) checkbox.checked = true;
        });
    }
});

// Color picker functionality
let selectedColors = [];

document.addEventListener('DOMContentLoaded', function() {
    const colorOptions = document.querySelectorAll('.color-option');
    const colorsInput = document.getElementById('colors');
    const selectedColorsDisplay = document.getElementById('selectedColorsDisplay');

    // Handle color option clicks
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            const colorName = this.dataset.color;

            if (this.classList.contains('selected')) {
                // Remove color
                this.classList.remove('selected');
                selectedColors = selectedColors.filter(color => color !== colorName);
            } else {
                // Add color
                this.classList.add('selected');
                if (!selectedColors.includes(colorName)) {
                    selectedColors.push(colorName);
                }
            }

            updateColorsDisplay();
            updateColorsInput();
        });
    });

    // Handle manual input changes
    colorsInput.addEventListener('input', function() {
        const inputColors = this.value.split(',').map(c => c.trim().toLowerCase()).filter(c => c);

        // Reset visual selection
        colorOptions.forEach(option => option.classList.remove('selected'));
        selectedColors = [];

        // Select matching colors in the picker
        inputColors.forEach(inputColor => {
            const matchingOption = Array.from(colorOptions).find(option =>
                option.dataset.color.toLowerCase() === inputColor
            );
            if (matchingOption) {
                matchingOption.classList.add('selected');
                selectedColors.push(matchingOption.dataset.color);
            } else {
                // Add custom color
                selectedColors.push(inputColor);
            }
        });

        updateColorsDisplay();
    });

    function updateColorsDisplay() {
        if (selectedColors.length === 0) {
            selectedColorsDisplay.innerHTML = '<span class="text-muted">No colors selected</span>';
            return;
        }

        const colorMap = {
            'black': '#000000', 'white': '#ffffff', 'gray': '#808080',
            'red': '#dc3545', 'blue': '#007bff', 'green': '#28a745',
            'yellow': '#ffc107', 'orange': '#fd7e14', 'purple': '#6f42c1',
            'pink': '#e83e8c', 'brown': '#8b4513', 'beige': '#f5f5dc',
            'navy': '#000080', 'maroon': '#800000', 'olive': '#808000',
            'teal': '#008080', 'cream': '#fffdd0', 'tan': '#d2b48c',
            'khaki': '#f0e68c', 'coral': '#ff7f50', 'salmon': '#fa8072',
            'lavender': '#e6e6fa', 'mint': '#98fb98', 'peach': '#ffcba4'
        };

        const colorTags = selectedColors.map(color => {
            const colorValue = colorMap[color.toLowerCase()] || '#6c757d';
            return `
                <div class="selected-color-tag">
                    <div class="color-dot" style="background-color: ${colorValue}"></div>
                    ${color.charAt(0).toUpperCase() + color.slice(1)}
                    <span class="remove-color" onclick="removeColor('${color}')">&times;</span>
                </div>
            `;
        }).join('');

        selectedColorsDisplay.innerHTML = colorTags;
    }

    function updateColorsInput() {
        colorsInput.value = selectedColors.join(', ');
    }

    // Make removeColor function global
    window.removeColor = function(colorToRemove) {
        selectedColors = selectedColors.filter(color => color !== colorToRemove);

        // Remove visual selection
        const matchingOption = Array.from(colorOptions).find(option =>
            option.dataset.color === colorToRemove
        );
        if (matchingOption) {
            matchingOption.classList.remove('selected');
        }

        updateColorsDisplay();
        updateColorsInput();
    };
});
</script>
{% endblock %}
