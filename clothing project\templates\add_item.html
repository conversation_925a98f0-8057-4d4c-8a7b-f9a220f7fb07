{% extends "base.html" %}

{% block title %}Add Item - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-plus-circle me-3"></i>Add New Item</h1>
    <p>Add a new clothing item to your personal wardrobe</p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="addItemForm">
                    <!-- Photo Upload -->
                    <div class="mb-4">
                        <label for="photo" class="form-label">
                            <i class="fas fa-camera me-2"></i>Item Photo
                        </label>
                        <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                            <div id="photoPreview" class="photo-preview">
                                <i class="fas fa-camera fa-3x text-muted mb-2"></i>
                                <p class="text-muted">Click to upload photo</p>
                                <small class="text-muted">Supports: JPG, PNG, GIF, WebP (Max 16MB)</small>
                            </div>
                        </div>
                        <input type="file" class="form-control d-none" id="photo" name="photo" 
                               accept="image/*" onchange="previewPhoto(this)">
                    </div>

                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="name" class="form-label">Item Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   placeholder="e.g., Blue Denim Jacket">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="top">Top</option>
                                <option value="bottom">Bottom</option>
                                <option value="dress">Dress</option>
                                <option value="outerwear">Outerwear</option>
                                <option value="shoes">Shoes</option>
                                <option value="accessory">Accessory</option>
                            </select>
                        </div>
                    </div>

                    <!-- Colors -->
                    <div class="mb-3">
                        <label for="colors" class="form-label">Colors *</label>
                        <input type="text" class="form-control" id="colors" name="colors" required 
                               placeholder="e.g., blue, white, navy (separate with commas)">
                        <div class="form-text">Enter the main colors of this item, separated by commas</div>
                    </div>

                    <!-- Brand and Details -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="brand" class="form-label">Brand</label>
                            <input type="text" class="form-control" id="brand" name="brand" 
                                   placeholder="e.g., Levi's">
                        </div>
                        <div class="col-md-4">
                            <label for="size" class="form-label">Size</label>
                            <input type="text" class="form-control" id="size" name="size" 
                                   placeholder="e.g., M, 32, 8.5">
                        </div>
                        <div class="col-md-4">
                            <label for="material" class="form-label">Material</label>
                            <input type="text" class="form-control" id="material" name="material" 
                                   placeholder="e.g., Cotton, Denim">
                        </div>
                    </div>

                    <!-- Purchase Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                        </div>
                        <div class="col-md-6">
                            <label for="cost" class="form-label">Cost ($)</label>
                            <input type="number" class="form-control" id="cost" name="cost" 
                                   step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>

                    <!-- Style Categories -->
                    <div class="mb-3">
                        <label class="form-label">Styles *</label>
                        <div class="form-text mb-2">Select all styles that apply to this item</div>
                        <div class="row">
                            {% for style in styles %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="style_{{ style.value }}" name="styles" value="{{ style.value }}">
                                    <label class="form-check-label" for="style_{{ style.value }}">
                                        {{ style.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Occasions -->
                    <div class="mb-3">
                        <label class="form-label">Suitable Occasions *</label>
                        <div class="form-text mb-2">Select occasions where this item would be appropriate</div>
                        <div class="row">
                            {% for occasion in occasions %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="occasion_{{ occasion.value }}" name="occasions" value="{{ occasion.value }}">
                                    <label class="form-check-label" for="occasion_{{ occasion.value }}">
                                        {{ occasion.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label class="form-label">Weather Conditions *</label>
                        <div class="form-text mb-2">Select weather conditions suitable for this item</div>
                        <div class="row">
                            {% for weather in weather_options %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="weather_{{ weather.value }}" name="weather" value="{{ weather.value }}">
                                    <label class="form-check-label" for="weather_{{ weather.value }}">
                                        {{ weather.value.replace('_', ' ').title() }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Ratings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="formality" class="form-label">Formality Level</label>
                            <input type="range" class="form-range" id="formality" name="formality" 
                                   min="1" max="10" value="5" oninput="updateRangeValue('formality')">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Casual</small>
                                <small class="text-muted">Value: <span id="formalityValue">5</span></small>
                                <small class="text-muted">Formal</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="versatility" class="form-label">Versatility</label>
                            <input type="range" class="form-range" id="versatility" name="versatility" 
                                   min="1" max="10" value="5" oninput="updateRangeValue('versatility')">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Specific</small>
                                <small class="text-muted">Value: <span id="versatilityValue">5</span></small>
                                <small class="text-muted">Versatile</small>
                            </div>
                        </div>
                    </div>

                    <!-- Condition -->
                    <div class="mb-3">
                        <label for="condition" class="form-label">Condition</label>
                        <select class="form-select" id="condition" name="condition">
                            <option value="excellent">Excellent</option>
                            <option value="good" selected>Good</option>
                            <option value="fair">Fair</option>
                            <option value="poor">Poor</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Any additional notes about this item..."></textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Add Item
                        </button>
                        <a href="{{ url_for('view_closet') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.photo-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.photo-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(108, 92, 231, 0.05);
}

.photo-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 10px;
    object-fit: cover;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-range::-webkit-slider-thumb {
    background: var(--primary-color);
}

.form-range::-moz-range-thumb {
    background: var(--primary-color);
    border: none;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewPhoto(input) {
    const preview = document.getElementById('photoPreview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" style="max-width: 100%; max-height: 200px; border-radius: 10px; object-fit: cover;">
                <p class="text-muted mt-2">Click to change photo</p>
            `;
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

function updateRangeValue(rangeName) {
    const range = document.getElementById(rangeName);
    const valueSpan = document.getElementById(rangeName + 'Value');
    valueSpan.textContent = range.value;
}

// Form validation
document.getElementById('addItemForm').addEventListener('submit', function(e) {
    // Check if at least one style is selected
    const styles = document.querySelectorAll('input[name="styles"]:checked');
    if (styles.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one style for this item.', 'error');
        return;
    }
    
    // Check if at least one occasion is selected
    const occasions = document.querySelectorAll('input[name="occasions"]:checked');
    if (occasions.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one occasion for this item.', 'error');
        return;
    }
    
    // Check if at least one weather condition is selected
    const weather = document.querySelectorAll('input[name="weather"]:checked');
    if (weather.length === 0) {
        e.preventDefault();
        showAlert('Please select at least one weather condition for this item.', 'error');
        return;
    }
});

// Auto-suggest based on category
document.getElementById('category').addEventListener('change', function() {
    const category = this.value;
    const suggestions = {
        'top': {
            styles: ['casual', 'business', 'classic'],
            occasions: ['casual', 'work', 'social'],
            weather: ['mild', 'warm']
        },
        'bottom': {
            styles: ['casual', 'business', 'classic'],
            occasions: ['casual', 'work', 'social'],
            weather: ['mild', 'warm', 'cool']
        },
        'dress': {
            styles: ['romantic', 'classic', 'trendy'],
            occasions: ['formal', 'social', 'date'],
            weather: ['warm', 'mild']
        },
        'outerwear': {
            styles: ['classic', 'casual', 'edgy'],
            occasions: ['casual', 'work', 'outdoor'],
            weather: ['cool', 'cold']
        },
        'shoes': {
            styles: ['classic', 'casual', 'sporty'],
            occasions: ['casual', 'work', 'exercise'],
            weather: ['mild', 'warm', 'cool']
        },
        'accessory': {
            styles: ['classic', 'trendy', 'minimalist'],
            occasions: ['casual', 'formal', 'social'],
            weather: ['mild', 'warm', 'cool', 'cold']
        }
    };
    
    if (suggestions[category]) {
        // Auto-check suggested options
        const suggestion = suggestions[category];
        
        // Clear all checkboxes first
        document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
        
        // Check suggested styles
        suggestion.styles.forEach(style => {
            const checkbox = document.getElementById(`style_${style}`);
            if (checkbox) checkbox.checked = true;
        });
        
        // Check suggested occasions
        suggestion.occasions.forEach(occasion => {
            const checkbox = document.getElementById(`occasion_${occasion}`);
            if (checkbox) checkbox.checked = true;
        });
        
        // Check suggested weather
        suggestion.weather.forEach(weather => {
            const checkbox = document.getElementById(`weather_${weather}`);
            if (checkbox) checkbox.checked = true;
        });
    }
});
</script>
{% endblock %}
