{% extends "base.html" %}

{% block title %}Dashboard - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-tachometer-alt me-3"></i>Your Closet Dashboard</h1>
    <p>Welcome to your personal style assistant! Manage your wardrobe and discover perfect outfits.</p>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.total_items }}</div>
            <div class="stats-label">Total Items</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, var(--success-color), #55a3ff);">
            <div class="stats-number">{{ stats.total_outfits }}</div>
            <div class="stats-label">Outfits Created</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, var(--accent-color), #ff6b9d);">
            <div class="stats-number">{{ stats.favorite_outfits }}</div>
            <div class="stats-label">Favorites</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, var(--warning-color), #ffa726);">
            <div class="stats-number">${{ "%.0f"|format(stats.total_value) }}</div>
            <div class="stats-label">Wardrobe Value</div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title mb-4">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_item') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus-circle me-2"></i>Add New Item
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('generate_outfit') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-magic me-2"></i>Generate Outfit
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('view_closet') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-eye me-2"></i>Browse Closet
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('search_outfits') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-search-dollar me-2"></i>Search Online
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="quickOutfitSuggestion()">
                            <i class="fas fa-random me-2"></i>Quick Suggestion
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="quickOnlineSearch()">
                            <i class="fas fa-shopping-bag me-2"></i>Quick Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Breakdown -->
{% if stats.categories %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-chart-pie me-2"></i>Wardrobe Breakdown
                </h5>
                <div class="category-breakdown">
                    {% for category, count in stats.categories.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-capitalize">
                            <i class="fas fa-{{ 'tshirt' if category == 'top' else 'shoe-prints' if category == 'shoes' else 'vest' if category == 'outerwear' else 'user-tie' if category == 'dress' else 'circle' }} me-2"></i>
                            {{ category.replace('_', ' ').title() }}
                        </span>
                        <span class="badge bg-primary rounded-pill">{{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-star me-2"></i>Wardrobe Insights
                </h5>
                
                {% if stats.most_worn %}
                <div class="mb-3">
                    <h6 class="text-muted">Most Worn Item</h6>
                    <div class="d-flex align-items-center">
                        {% if stats.most_worn.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/' + stats.most_worn.photo_filename) }}" 
                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-tshirt text-muted"></i>
                        </div>
                        {% endif %}
                        <div>
                            <strong>{{ stats.most_worn.name }}</strong><br>
                            <small class="text-muted">Worn {{ stats.most_worn.wear_count }} times</small>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <h6 class="text-muted">Recent Additions</h6>
                    {% for item in stats.recent_additions[:3] %}
                    <div class="d-flex align-items-center mb-2">
                        {% if item.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                             class="rounded me-2" style="width: 30px; height: 30px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                             style="width: 30px; height: 30px;">
                            <i class="fas fa-tshirt text-muted" style="font-size: 0.8rem;"></i>
                        </div>
                        {% endif %}
                        <small>{{ item.name }}</small>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Outfits -->
{% if recent_outfits %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-clock me-2"></i>Recent Outfits
                </h5>
                <div class="row">
                    {% for outfit in recent_outfits %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-title mb-0">{{ outfit.occasion.value.title() }} Outfit</h6>
                                    <span class="score-badge position-static">{{ "%.0f"|format(outfit.overall_score * 100) }}%</span>
                                </div>
                                
                                <div class="outfit-preview mb-3">
                                    {% for item in outfit.items %}
                                        {% if item.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                             class="outfit-item" alt="{{ item.name }}" title="{{ item.name }}">
                                        {% else %}
                                        <div class="outfit-item-placeholder" title="{{ item.name }}">
                                            <i class="fas fa-tshirt"></i>
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                
                                <p class="card-text small text-muted">
                                    {{ outfit.description }}
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {{ outfit.date_created[:10] }}
                                    </small>
                                    <button class="favorite-btn position-static {{ 'active' if outfit.is_favorite else '' }}" 
                                            onclick="toggleFavorite('{{ outfit.id }}', this)">
                                        <i class="fa{{ 's' if outfit.is_favorite else 'r' }} fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('outfit_history') }}" class="btn btn-outline-primary">
                        View All Outfits <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card text-center">
            <div class="card-body py-5">
                <i class="fas fa-magic fa-3x text-muted mb-3"></i>
                <h5>No Outfits Yet</h5>
                <p class="text-muted">Start by adding some clothing items to your closet, then generate your first outfit!</p>
                <a href="{{ url_for('add_item') }}" class="btn btn-primary me-2">Add Items</a>
                <a href="{{ url_for('generate_outfit') }}" class="btn btn-outline-primary">Generate Outfit</a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Outfit Modal -->
<div class="modal fade" id="quickOutfitModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-random me-2"></i>Quick Outfit Suggestion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="quickOutfitContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Generating your perfect outfit...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickOutfitSuggestion() {
    const modal = new bootstrap.Modal(document.getElementById('quickOutfitModal'));
    modal.show();
    
    // Reset modal content
    document.getElementById('quickOutfitContent').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Generating your perfect outfit...</p>
        </div>
    `;
    
    // Fetch quick suggestion
    fetch('/api/outfit-suggestions?occasion=casual&weather=mild')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const outfit = data.outfit;
                let itemsHtml = '';
                
                outfit.items.forEach(item => {
                    if (item.photo) {
                        itemsHtml += `<img src="/static/uploads/${item.photo}" class="outfit-item me-2 mb-2" alt="${item.name}" title="${item.name}">`;
                    } else {
                        itemsHtml += `<div class="outfit-item-placeholder me-2 mb-2" title="${item.name}"><i class="fas fa-tshirt"></i></div>`;
                    }
                });
                
                document.getElementById('quickOutfitContent').innerHTML = `
                    <div class="text-center">
                        <h6 class="mb-3">${outfit.description}</h6>
                        <div class="outfit-preview justify-content-center mb-3">
                            ${itemsHtml}
                        </div>
                        <div class="score-badge position-static mb-3">${Math.round(outfit.score * 100)}%</div>
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-primary" onclick="saveQuickOutfit(${JSON.stringify(outfit).replace(/"/g, '&quot;')})">
                                <i class="fas fa-save me-1"></i>Save Outfit
                            </button>
                            <a href="/generate-outfit" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-1"></i>Customize
                            </a>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('quickOutfitContent').innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h6>No Suitable Outfits Found</h6>
                        <p class="text-muted">${data.message || 'Try adding more items to your closet or adjust your preferences.'}</p>
                        <a href="/add-item" class="btn btn-primary">Add Items</a>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('quickOutfitContent').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h6>Error Loading Outfit</h6>
                    <p class="text-muted">Please try again later.</p>
                </div>
            `;
        });
}

function saveQuickOutfit(outfit) {
    // This would need to be implemented based on your outfit data structure
    showAlert('Quick outfit saved!', 'success');
    bootstrap.Modal.getInstance(document.getElementById('quickOutfitModal')).hide();
}

function quickOnlineSearch() {
    // Quick search suggestions
    const searchQueries = [
        'casual summer outfit',
        'business professional attire',
        'date night dress',
        'cozy winter sweater',
        'workout activewear',
        'elegant evening gown'
    ];

    // Pick a random search query
    const randomQuery = searchQueries[Math.floor(Math.random() * searchQueries.length)];

    // Redirect to search page with the query
    const searchUrl = `/search-outfits?style_query=${encodeURIComponent(randomQuery)}`;
    window.location.href = searchUrl;
}
</script>
{% endblock %}
