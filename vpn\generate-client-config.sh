#!/bin/bash
set -e

# Privacy VPN Client Configuration Generator
echo "🔐 Generating Privacy VPN Client Configuration..."

# Configuration variables
CLIENT_NAME="${1:-privacy-client}"
SERVER_HOST="${2:-your-server-ip}"
SERVER_PORT="${3:-1194}"
PROTOCOL="${4:-udp}"

echo "📋 Configuration:"
echo "  Client Name: $CLIENT_NAME"
echo "  Server: $SERVER_HOST:$SERVER_PORT"
echo "  Protocol: $PROTOCOL"
echo ""

# Generate client configuration
cat > "${CLIENT_NAME}.ovpn" << EOF
##############################################
# Privacy VPN Client Configuration
# Generated: $(date)
##############################################

# Client mode
client

# Protocol and server
dev tun
proto $PROTOCOL
remote $SERVER_HOST $SERVER_PORT

# Connection settings
resolv-retry infinite
nobind
persist-key
persist-tun

# Security settings
cipher AES-256-GCM
auth SHA256
key-direction 1
tls-version-min 1.2

# Privacy and security enhancements
# Prevent DNS leaks
dhcp-option DNS ********
dhcp-option DNS *******
block-outside-dns

# Route all traffic through VPN
redirect-gateway def1 bypass-dhcp

# Compression (optional, can be disabled for better security)
compress lz4-v2
push "compress lz4-v2"

# Logging (minimal for privacy)
verb 3
mute 20

# Connection reliability
keepalive 10 120
ping-timer-rem
persist-remote-ip

# Privacy features
# Disable IPv6 to prevent leaks
pull-filter ignore "dhcp-option DNS6"
pull-filter ignore "tun-ipv6"
pull-filter ignore "ifconfig-ipv6"

# Certificate and key data (to be replaced with actual certificates)
<ca>
# Certificate Authority certificate will be inserted here
# This should be replaced with the actual CA certificate from your VPN server
-----BEGIN CERTIFICATE-----
[CA_CERTIFICATE_CONTENT]
-----END CERTIFICATE-----
</ca>

<cert>
# Client certificate will be inserted here
# This should be replaced with the actual client certificate
-----BEGIN CERTIFICATE-----
[CLIENT_CERTIFICATE_CONTENT]
-----END CERTIFICATE-----
</cert>

<key>
# Client private key will be inserted here
# This should be replaced with the actual client private key
-----BEGIN PRIVATE KEY-----
[CLIENT_PRIVATE_KEY_CONTENT]
-----END PRIVATE KEY-----
</key>

<tls-auth>
# TLS authentication key will be inserted here
# This should be replaced with the actual TLS auth key
-----BEGIN OpenVPN Static key V1-----
[TLS_AUTH_KEY_CONTENT]
-----END OpenVPN Static key V1-----
</tls-auth>

##############################################
# Privacy and Security Notes:
#
# 1. DNS Leak Protection: This configuration
#    forces all DNS queries through the VPN
#
# 2. IPv6 Disabled: Prevents IPv6 leaks
#
# 3. Kill Switch: Connection will drop if
#    VPN disconnects (depending on client)
#
# 4. Strong Encryption: AES-256-GCM with
#    SHA256 authentication
#
# 5. No Logs: Server configured with no-logs
#    policy for maximum privacy
##############################################
EOF

echo "✅ Client configuration generated: ${CLIENT_NAME}.ovpn"
echo ""
echo "🔧 Next steps:"
echo "1. Replace certificate placeholders with actual certificates from your VPN server"
echo "2. Update SERVER_HOST with your actual server IP address"
echo "3. Import the .ovpn file into your OpenVPN client"
echo ""
echo "📱 Compatible clients:"
echo "  • Windows: OpenVPN GUI"
echo "  • macOS: Tunnelblick"
echo "  • Linux: OpenVPN"
echo "  • Android: OpenVPN Connect"
echo "  • iOS: OpenVPN Connect"
echo ""
echo "🛡️  Privacy features enabled in this configuration:"
echo "  ✅ DNS leak protection"
echo "  ✅ IPv6 leak prevention"
echo "  ✅ Traffic encryption (AES-256-GCM)"
echo "  ✅ Authentication (SHA256)"
echo "  ✅ All traffic routed through VPN"
echo ""

# Create a privacy-enhanced version
cat > "${CLIENT_NAME}-privacy.ovpn" << EOF
##############################################
# MAXIMUM PRIVACY VPN Client Configuration
# Generated: $(date)
##############################################

# Client mode with enhanced privacy
client

# Protocol and server
dev tun
proto $PROTOCOL
remote $SERVER_HOST $SERVER_PORT

# Connection settings
resolv-retry infinite
nobind
persist-key
persist-tun

# Maximum security settings
cipher AES-256-GCM
auth SHA256
key-direction 1
tls-version-min 1.2
tls-cipher TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384

# Enhanced privacy settings
# Multiple DNS servers for redundancy
dhcp-option DNS ********
dhcp-option DNS *******
dhcp-option DNS *******
block-outside-dns

# Route ALL traffic through VPN (maximum privacy)
redirect-gateway def1 bypass-dhcp
redirect-gateway def1 bypass-dns

# Disable compression for better security
compress

# Minimal logging for privacy
verb 1
mute 10

# Enhanced connection reliability
keepalive 10 60
ping-timer-rem
persist-remote-ip
connect-retry-max 3

# Maximum privacy filters
# Block all IPv6 to prevent leaks
pull-filter ignore "dhcp-option DNS6"
pull-filter ignore "tun-ipv6"
pull-filter ignore "ifconfig-ipv6"
pull-filter ignore "route-ipv6"

# Block WebRTC leaks (client-dependent)
# Note: This requires client support
script-security 2
up /etc/openvpn/update-resolv-conf
down /etc/openvpn/update-resolv-conf

# Certificate and key data (same as standard config)
<ca>
-----BEGIN CERTIFICATE-----
[CA_CERTIFICATE_CONTENT]
-----END CERTIFICATE-----
</ca>

<cert>
-----BEGIN CERTIFICATE-----
[CLIENT_CERTIFICATE_CONTENT]
-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----
[CLIENT_PRIVATE_KEY_CONTENT]
-----END PRIVATE KEY-----
</key>

<tls-auth>
-----BEGIN OpenVPN Static key V1-----
[TLS_AUTH_KEY_CONTENT]
-----END OpenVPN Static key V1-----
</tls-auth>

##############################################
# MAXIMUM PRIVACY CONFIGURATION
#
# This configuration prioritizes privacy over
# performance and includes additional security
# measures to prevent any data leaks.
#
# Features:
# • Multiple DNS servers
# • IPv6 completely disabled
# • Minimal logging
# • Enhanced encryption
# • Strict traffic routing
# • WebRTC leak protection
##############################################
EOF

echo "🔒 Maximum privacy configuration generated: ${CLIENT_NAME}-privacy.ovpn"
echo ""
echo "⚠️  Important privacy notes:"
echo "1. Use the -privacy.ovpn version for maximum anonymity"
echo "2. Disable WebRTC in your browser for complete protection"
echo "3. Use privacy-focused browsers (Tor Browser, Firefox with privacy settings)"
echo "4. Consider using a VPN chain for ultimate anonymity"
echo "5. Always verify your IP address after connecting: https://whatismyipaddress.com"
echo ""
echo "🎯 Privacy checklist after connecting:"
echo "  □ Check IP address has changed"
echo "  □ Verify DNS is not leaking: https://dnsleaktest.com"
echo "  □ Test WebRTC leaks: https://browserleaks.com/webrtc"
echo "  □ Confirm IPv6 is disabled: https://test-ipv6.com"
echo ""
echo "🚀 Ready to browse privately!"
