#!/bin/bash
set -e

echo "🚀 Starting Privacy VPN Server..."

# Create TUN device if it doesn't exist
if [ ! -c /dev/net/tun ]; then
    echo "⚠️  TUN device not available. Make sure to run with --device /dev/net/tun"
fi

# Set up iptables rules for privacy
echo "🔒 Setting up privacy firewall rules..."

# Enable IP forwarding
echo 1 > /proc/sys/net/ipv4/ip_forward

# Clear existing rules
iptables -F
iptables -t nat -F

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
iptables -A FORWARD -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow VPN traffic
iptables -A INPUT -p udp --dport 1194 -j ACCEPT
iptables -A INPUT -p tcp --dport 1194 -j ACCEPT

# Allow DNS (for privacy DNS servers)
iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT
iptables -A OUTPUT -p udp --dport 853 -j ACCEPT  # DNS over TLS
iptables -A OUTPUT -p tcp --dport 853 -j ACCEPT  # DNS over TLS

# Allow HTTPS for DNS over HTTPS
iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT

# NAT for VPN clients (will be configured by VPN server)
iptables -t nat -A POSTROUTING -s ********/24 -o eth0 -j MASQUERADE

# Forward VPN traffic
iptables -A FORWARD -s ********/24 -j ACCEPT
iptables -A FORWARD -d ********/24 -j ACCEPT

# Block common tracking and ads (basic privacy protection)
echo "🛡️  Setting up ad and tracker blocking..."

# Block common ad/tracking domains (simplified list)
BLOCKED_IPS=(
    "**************"  # Example tracking IP
    "**************"  # Example ad server IP
)

for ip in "${BLOCKED_IPS[@]}"; do
    iptables -A FORWARD -d "$ip" -j DROP
done

# Generate certificates if they don't exist
if [ ! -f /etc/vpn/certs/server.crt ]; then
    echo "🔐 Generating VPN certificates..."
    python cli/server_cli.py generate-certs --output /etc/vpn/certs
fi

# Start Unbound DNS resolver for privacy
echo "🌐 Starting privacy DNS resolver..."
unbound-checkconf /etc/unbound/unbound.conf
service unbound start

# Initialize VPN configuration
echo "⚙️  Initializing VPN configuration..."
python cli/server_cli.py init-config --config /etc/vpn/privacy-config.yaml

echo "✅ Privacy VPN Server setup complete!"

# Execute the main command
exec "$@"
