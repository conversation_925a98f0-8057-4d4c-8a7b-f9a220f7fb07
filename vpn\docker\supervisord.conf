[supervisord]
nodaemon=true
user=root
logfile=/var/log/vpn/supervisord.log
pidfile=/var/run/vpn/supervisord.pid

[program:unbound]
command=unbound -d -c /etc/unbound/unbound.conf
autostart=true
autorestart=true
stderr_logfile=/var/log/vpn/unbound.err.log
stdout_logfile=/var/log/vpn/unbound.out.log
user=root

[program:vpn-server]
command=python cli/server_cli.py start --config /etc/vpn/privacy-config.yaml
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/vpn/vpn-server.err.log
stdout_logfile=/var/log/vpn/vpn-server.out.log
user=root
environment=PYTHONPATH="/app"

[program:privacy-monitor]
command=python docker/privacy_monitor.py
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/vpn/privacy-monitor.err.log
stdout_logfile=/var/log/vpn/privacy-monitor.out.log
user=root
