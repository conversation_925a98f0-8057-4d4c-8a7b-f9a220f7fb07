# Python VPN Implementation

A modern, secure VPN solution built in Python with both client and server capabilities.

## Architecture Overview

This VPN implementation uses a client-server model with the following key components:

### Core Components
- **VPN Server**: Handles client connections, authentication, and traffic routing
- **VPN Client**: Connects to server, encrypts/decrypts traffic, manages local routing
- **Security Layer**: AES-256 encryption, RSA key exchange, certificate-based authentication
- **Network Manager**: Handles IP allocation, routing tables, and network interfaces
- **Configuration Manager**: Manages server/client configurations and profiles

### Security Features
- **Encryption**: AES-256-GCM for data encryption
- **Key Exchange**: RSA-4096 for initial key exchange, ECDH for perfect forward secrecy
- **Authentication**: Certificate-based client authentication
- **Integrity**: HMAC-SHA256 for message authentication

### Network Features
- **IP Allocation**: Dynamic IP assignment from configurable pools
- **Traffic Routing**: Full tunnel or split tunnel routing
- **DNS Management**: Custom DNS server configuration
- **Multi-platform**: Windows, Linux, macOS support

## Directory Structure

```
vpn/
├── README.md
├── requirements.txt
├── setup.py
├── config/
│   ├── server.yaml
│   ├── client.yaml
│   └── certificates/
├── src/
│   ├── __init__.py
│   ├── server/
│   │   ├── __init__.py
│   │   ├── vpn_server.py
│   │   ├── client_manager.py
│   │   └── network_manager.py
│   ├── client/
│   │   ├── __init__.py
│   │   ├── vpn_client.py
│   │   └── connection_manager.py
│   ├── security/
│   │   ├── __init__.py
│   │   ├── encryption.py
│   │   ├── authentication.py
│   │   └── certificates.py
│   ├── network/
│   │   ├── __init__.py
│   │   ├── routing.py
│   │   ├── interface.py
│   │   └── dns.py
│   └── utils/
│       ├── __init__.py
│       ├── config.py
│       ├── logger.py
│       └── protocol.py
├── cli/
│   ├── __init__.py
│   ├── server_cli.py
│   └── client_cli.py
├── web/
│   ├── __init__.py
│   ├── dashboard.py
│   └── templates/
└── tests/
    ├── __init__.py
    ├── test_server.py
    ├── test_client.py
    └── test_security.py
```

## Installation

### Prerequisites
- Python 3.8 or higher
- Administrative/root privileges (for network interface management)
- OpenSSL (for certificate generation)

### Install Dependencies
```bash
# Navigate to VPN directory
cd vpn

# Install Python dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### Initialize Configuration
```bash
# Initialize server configuration
python cli/server_cli.py init-config

# Initialize client configuration
python cli/client_cli.py init-config

# Generate certificates (placeholder)
python cli/server_cli.py generate-certs
```

## Quick Start

### Method 1: Using CLI Scripts

#### Server Setup
```bash
# Start VPN server
python cli/server_cli.py start

# Start server with custom config
python cli/server_cli.py start --config config/server.yaml

# Check server status
python cli/server_cli.py status

# List connected clients
python cli/server_cli.py clients
```

#### Client Setup
```bash
# Connect to VPN server
python cli/client_cli.py connect

# Connect to specific server
python cli/client_cli.py connect --server ************* --port 1194

# Check connection status
python cli/client_cli.py status

# Test connection to server
python cli/client_cli.py test ************* 1194

# Disconnect
python cli/client_cli.py disconnect
```

### Method 2: Using Demo Script
```bash
# Run interactive demo
python demo.py
```

### Method 3: Programmatic Usage
```python
from src.server.vpn_server import VPNServer
from src.client.vpn_client import VPNClient

# Start server
server = VPNServer()
server.start()

# Connect client
client = VPNClient()
client.connect("127.0.0.1", 1194)
```

## Configuration

### Server Configuration (config/server.yaml)
```yaml
server:
  host: "0.0.0.0"
  port: 1194
  protocol: "udp"
  
network:
  subnet: "********/24"
  dns_servers: ["*******", "*******"]
  
security:
  cert_file: "config/certificates/server.crt"
  key_file: "config/certificates/server.key"
  ca_file: "config/certificates/ca.crt"
```

### Client Configuration (config/client.yaml)
```yaml
client:
  server_host: "your-server-ip"
  server_port: 1194
  protocol: "udp"
  
security:
  cert_file: "config/certificates/client.crt"
  key_file: "config/certificates/client.key"
  ca_file: "config/certificates/ca.crt"
```

## Features

- ✅ Secure encryption (AES-256-GCM)
- ✅ Certificate-based authentication
- ✅ Dynamic IP allocation
- ✅ Traffic routing and forwarding
- ✅ DNS management
- ✅ Cross-platform support
- ✅ CLI management interface
- ✅ Web dashboard (optional)
- ✅ Logging and monitoring

## Requirements

- Python 3.8+
- Administrative privileges (for network interface management)
- OpenSSL (for certificate generation)

## Testing

### Run Tests
```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/test_basic.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

### Manual Testing
```bash
# Test server startup
python cli/server_cli.py start --config config/server.yaml

# In another terminal, test client connection
python cli/client_cli.py connect --server 127.0.0.1 --port 1194
```

## Troubleshooting

### Common Issues

#### Permission Denied Errors
```bash
# On Linux/macOS, run with sudo for network interface access
sudo python cli/server_cli.py start
sudo python cli/client_cli.py connect
```

#### Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep 1194

# Kill the process or change port in config
```

#### Certificate Errors
```bash
# Regenerate certificates
python cli/server_cli.py generate-certs --output config/certificates
```

#### Connection Timeouts
- Check firewall settings
- Verify server is running: `python cli/server_cli.py status`
- Test connectivity: `python cli/client_cli.py test <server_ip> <port>`

### Debug Mode
```bash
# Enable debug logging
export VPN_LOG_LEVEL=DEBUG
python cli/server_cli.py start
```

## Platform-Specific Notes

### Windows
- Requires TAP-Windows driver for TUN interface
- Run Command Prompt as Administrator
- Windows Defender may block network changes

### Linux
- Requires root privileges for network configuration
- Install `iproute2` package: `sudo apt install iproute2`
- May need to disable NetworkManager for VPN interface

### macOS
- Requires TunTap driver installation
- Run with `sudo` for network changes
- May need to allow network extensions in System Preferences

## Security Considerations

⚠️ **Important Security Notes:**

1. **Certificates**: The generated certificates are placeholders. For production use:
   ```bash
   # Generate proper CA certificate
   openssl genrsa -out ca.key 4096
   openssl req -new -x509 -days 365 -key ca.key -out ca.crt

   # Generate server certificate
   openssl genrsa -out server.key 4096
   openssl req -new -key server.key -out server.csr
   openssl x509 -req -days 365 -in server.csr -CA ca.crt -CAkey ca.key -out server.crt
   ```

2. **Firewall**: Configure firewall rules appropriately
3. **Authentication**: Implement proper client authentication
4. **Encryption**: Verify encryption settings in production
5. **Logging**: Secure log files and rotate regularly

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make changes and add tests
4. Run tests: `python -m pytest`
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section above
- Review the logs in `logs/` directory
- Open an issue on the project repository

## Roadmap

- [ ] Web-based management interface
- [ ] Advanced routing features
- [ ] Multi-platform TUN/TAP support
- [ ] Certificate management tools
- [ ] Performance optimizations
- [ ] Docker containerization
- [ ] Kubernetes deployment manifests
