{% extends "base.html" %}

{% block title %}Generate Outfit - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-magic me-3"></i>Generate Perfect Outfit</h1>
    <p>Let AI create stylish outfits from your personal wardrobe</p>
</div>

<div class="row">
    <!-- Generation Form -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sliders-h me-2"></i>Outfit Preferences
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="outfitForm">
                    <!-- Occasion -->
                    <div class="mb-3">
                        <label for="occasion" class="form-label">Occasion *</label>
                        <select class="form-select" id="occasion" name="occasion" required>
                            <option value="">Select Occasion</option>
                            {% for occasion in occasions %}
                            <option value="{{ occasion.value }}">{{ occasion.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label for="weather" class="form-label">Weather *</label>
                        <select class="form-select" id="weather" name="weather" required>
                            <option value="">Select Weather</option>
                            {% for weather in weather_options %}
                            <option value="{{ weather.value }}">{{ weather.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Style Preference -->
                    <div class="mb-3">
                        <label for="style" class="form-label">Preferred Style</label>
                        <select class="form-select" id="style" name="style">
                            <option value="">Any Style</option>
                            {% for style in styles %}
                            <option value="{{ style.value }}">{{ style.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Leave blank to consider all styles</div>
                    </div>

                    <!-- Color Preference -->
                    <div class="mb-3">
                        <label for="color_preference" class="form-label">Color Preference</label>
                        <input type="text" class="form-control" id="color_preference" name="color_preference" 
                               placeholder="e.g., blue, red, neutral">
                        <div class="form-text">Specify a preferred color or leave blank</div>
                    </div>

                    <!-- Quick Presets -->
                    <div class="mb-4">
                        <label class="form-label">Quick Presets</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('work')">
                                <i class="fas fa-briefcase me-1"></i>Work Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('casual')">
                                <i class="fas fa-coffee me-1"></i>Casual Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('date')">
                                <i class="fas fa-heart me-1"></i>Date Night
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('formal')">
                                <i class="fas fa-star me-1"></i>Formal Event
                            </button>
                        </div>
                    </div>

                    <!-- Generation Mode -->
                    <div class="mb-4">
                        <label class="form-label">Generation Mode</label>
                        <div class="generation-modes">
                            <div class="form-check generation-option">
                                <input class="form-check-input" type="radio" name="generation_mode" id="auto_mode" value="auto" checked>
                                <label class="form-check-label" for="auto_mode">
                                    <div class="option-header">
                                        <i class="fas fa-magic me-2 text-primary"></i>
                                        <strong>Auto Generate</strong>
                                    </div>
                                    <div class="option-description">
                                        Let our AI create complete, harmonious outfits for you. Perfect when you want a full look with tops, bottoms, shoes, and accessories automatically coordinated.
                                    </div>
                                    <div class="option-features">
                                        <span class="feature-tag">Complete Outfits</span>
                                        <span class="feature-tag">AI Coordination</span>
                                        <span class="feature-tag">Style Harmony</span>
                                    </div>
                                </label>
                            </div>
                            <div class="form-check generation-option">
                                <input class="form-check-input" type="radio" name="generation_mode" id="custom_mode" value="custom">
                                <label class="form-check-label" for="custom_mode">
                                    <div class="option-header">
                                        <i class="fas fa-tools me-2 text-success"></i>
                                        <strong>Custom Builder</strong>
                                    </div>
                                    <div class="option-description">
                                        Choose exactly which types of clothing items you want. Great for specific needs like "just a top and bottom" or "only shoes" or "dress with accessories".
                                    </div>
                                    <div class="option-features">
                                        <span class="feature-tag">Flexible Selection</span>
                                        <span class="feature-tag">Specific Needs</span>
                                        <span class="feature-tag">Custom Combinations</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Item Selection (hidden by default) -->
                    <div id="customItemSelection" class="mb-3" style="display: none;">
                        <label class="form-label">Select Item Types</label>
                        <div class="custom-items-grid">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_tops" name="custom_items" value="top">
                                <label class="form-check-label" for="include_tops">
                                    <i class="fas fa-tshirt me-1"></i>Tops
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_bottoms" name="custom_items" value="bottom">
                                <label class="form-check-label" for="include_bottoms">
                                    <i class="fas fa-user-tie me-1"></i>Bottoms
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_dresses" name="custom_items" value="dress">
                                <label class="form-check-label" for="include_dresses">
                                    <i class="fas fa-female me-1"></i>Dresses
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_shoes" name="custom_items" value="shoes">
                                <label class="form-check-label" for="include_shoes">
                                    <i class="fas fa-shoe-prints me-1"></i>Shoes
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_outerwear" name="custom_items" value="outerwear">
                                <label class="form-check-label" for="include_outerwear">
                                    <i class="fas fa-coat-arms me-1"></i>Outerwear
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_accessories" name="custom_items" value="accessories">
                                <label class="form-check-label" for="include_accessories">
                                    <i class="fas fa-gem me-1"></i>Accessories
                                </label>
                            </div>
                        </div>
                        <div class="form-text">Select at least one item type for your custom outfit</div>

                        <!-- Quick Custom Presets -->
                        <div class="mt-2">
                            <small class="text-muted">Quick presets:</small><br>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top', 'bottom'])">
                                <i class="fas fa-tshirt me-1"></i>Top + Bottom
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['dress'])">
                                <i class="fas fa-female me-1"></i>Dress Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top'])">
                                <i class="fas fa-tshirt me-1"></i>Top Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['shoes'])">
                                <i class="fas fa-shoe-prints me-1"></i>Shoes Only
                            </button>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>Generate Outfits
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Results Area -->
    <div class="col-lg-8">
        <div id="resultsArea">
            <!-- Welcome Message -->
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-tshirt fa-4x text-muted mb-4"></i>
                    <h4>Ready to Create Amazing Outfits?</h4>
                    <p class="text-muted mb-4">
                        Select your preferences on the left and let our AI stylist create perfect outfits from your personal wardrobe.
                    </p>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Smart Color Matching</h6>
                            <p class="small text-muted">AI analyzes color harmony for perfect combinations</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Style Consistency</h6>
                            <p class="small text-muted">Ensures all pieces work together stylistically</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Weather Appropriate</h6>
                            <p class="small text-muted">Considers weather conditions for practical choices</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Occasion Perfect</h6>
                            <p class="small text-muted">Matches formality level to your occasion</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.custom-items-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.custom-items-grid .form-check {
    margin-bottom: 0;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.custom-items-grid .form-check:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.custom-items-grid .form-check-input:checked + .form-check-label {
    color: var(--primary-color);
    font-weight: 500;
}

.outfit-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.outfit-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Rating Stars */
.rating-stars {
    font-size: 1.2rem;
    cursor: pointer;
}

.rating-stars i {
    color: #ddd;
    transition: color 0.2s ease;
    margin-right: 0.2rem;
}

.rating-stars i:hover,
.rating-stars i.active {
    color: #ffc107;
}

.rating-stars-large {
    font-size: 2rem;
    cursor: pointer;
}

.rating-stars-large i {
    color: #ddd;
    transition: all 0.2s ease;
    margin: 0 0.3rem;
}

.rating-stars-large i:hover,
.rating-stars-large i.active {
    color: #ffc107;
    transform: scale(1.1);
}

/* Enhanced Generation Mode Options */
.generation-modes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.generation-option {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.generation-option:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.generation-option input[type="radio"]:checked + label {
    color: #007bff;
}

.generation-option input[type="radio"]:checked + label .option-header {
    color: #007bff;
}

.generation-option:has(input[type="radio"]:checked) {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.option-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.option-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.option-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

/* Color Palette Styles */
.color-palette {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.color-palette-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
    font-weight: 500;
}

.color-circles {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.color-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
}

.color-circle:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.color-circle[style*="ffffff"] {
    border-color: #dee2e6;
}

.color-circle[style*="000000"] {
    border-color: #6c757d;
}

/* Enhanced Score Breakdown */
.score-breakdown {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.score-breakdown-header {
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.score-item {
    background: white;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.score-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
}

.score-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 80px;
}

.score-percentage {
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
    min-width: 35px;
    text-align: right;
}

.score-bar {
    width: 40px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #ffc107 50%, #dc3545 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.score-explanation {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.score-explanation small {
    font-size: 0.8rem;
    line-height: 1.4;
    color: #495057 !important;
    font-style: italic;
}

@media (max-width: 768px) {
    .generation-option {
        padding: 1rem;
    }

    .option-features {
        justify-content: center;
    }

    .color-circles {
        gap: 0.3rem;
    }

    .color-circle {
        width: 20px;
        height: 20px;
    }
}

.outfit-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.score-breakdown {
    font-size: 0.85rem;
}

.outfit-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 15px 0;
}

.outfit-item-thumb {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.outfit-item-thumb-placeholder {
    width: 70px;
    height: 70px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #6c757d;
}

.loading-spinner {
    text-align: center;
    padding: 60px 20px;
}

.preset-btn {
    transition: all 0.2s ease;
}

.preset-btn:hover {
    transform: translateX(5px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function setPreset(type) {
    const presets = {
        'work': {
            occasion: 'business',
            weather: 'mild',
            style: 'business',
            color: ''
        },
        'casual': {
            occasion: 'casual',
            weather: 'mild',
            style: 'casual',
            color: ''
        },
        'date': {
            occasion: 'date',
            weather: 'mild',
            style: 'romantic',
            color: ''
        },
        'formal': {
            occasion: 'formal',
            weather: 'mild',
            style: 'elegant',
            color: ''
        }
    };
    
    const preset = presets[type];
    if (preset) {
        document.getElementById('occasion').value = preset.occasion;
        document.getElementById('weather').value = preset.weather;
        document.getElementById('style').value = preset.style;
        document.getElementById('color_preference').value = preset.color;
    }
}

document.getElementById('outfitForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading state
    document.getElementById('resultsArea').innerHTML = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5>Creating Your Perfect Outfits...</h5>
            <p class="text-muted">Our AI stylist is analyzing your wardrobe and preferences</p>
        </div>
    `;
    
    // Submit form
    const formData = new FormData(this);
    
    // Add AJAX header to get JSON response
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response received:', response);
        return response.json();
    })
    .then(data => {
        console.log('Data received:', data);
        if (data.success && data.outfits) {
            // Store outfit data globally
            window.outfitData = data.outfits;
            console.log('Stored outfit data:', window.outfitData);

            // Render outfits using the data
            renderOutfits(data);

            // Initialize button event listeners with a longer delay
            setTimeout(() => {
                console.log('Initializing buttons...');
                initializeOutfitButtons();
            }, 500);
        } else {
            console.error('No outfits in response:', data);
            document.getElementById('resultsArea').innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-search fa-4x text-muted mb-4"></i>
                        <h4>No Suitable Outfits Found</h4>
                        <p class="text-muted mb-4">We couldn't find any outfits that match your criteria.</p>
                        <button class="btn btn-primary" onclick="location.reload()">Try Different Settings</button>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('resultsArea').innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>Error Generating Outfits</h5>
                    <p class="text-muted">Please try again or check your internet connection.</p>
                    <button class="btn btn-primary" onclick="location.reload()">Try Again</button>
                </div>
            </div>
        `;
    });
});

function saveOutfitFromGeneration(outfitData, button) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(outfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
            showAlert('Outfit saved to your history!', 'success');
        } else {
            button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
            button.disabled = false;
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
        button.disabled = false;
        showAlert('Error saving outfit', 'error');
    });
}

function regenerateOutfits() {
    document.getElementById('outfitForm').dispatchEvent(new Event('submit'));
}

// Function to render outfits from JSON data
function renderOutfits(data) {
    const { outfits, occasion, weather, preferred_style, generation_mode, custom_items } = data;

    let html = `
        <!-- Action Bar -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>${outfits.length} Outfits Generated
                        </h5>
                        <small class="text-muted">
                            ${generation_mode === 'custom' && custom_items ?
                                'Custom: ' + custom_items.join(', ').replace(/([a-z])([A-Z])/g, '$1 $2') :
                                'Auto-generated complete outfits'}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2 justify-content-md-end">
                            <button class="btn btn-outline-primary" onclick="regenerateOutfits()">
                                <i class="fas fa-redo me-1"></i>Regenerate
                            </button>
                            <button class="btn btn-outline-secondary" onclick="modifySearch()">
                                <i class="fas fa-sliders-h me-1"></i>Modify Search
                            </button>
                            <button class="btn btn-outline-success" onclick="saveAllOutfits()">
                                <i class="fas fa-save me-1"></i>Save All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outfit Results -->
        <div class="row mb-4">
    `;

    outfits.forEach((outfit, index) => {
        html += `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card outfit-card h-100">
                    <div class="position-relative">
                        <!-- Outfit Score -->
                        <div class="outfit-score">
                            ${Math.round(outfit.overall_score * 100)}%
                        </div>

                        <!-- Favorite Button -->
                        <button class="favorite-btn" onclick="toggleFavorite('${outfit.id}', this)">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>

                    <div class="card-body">
                        <!-- Outfit Items -->
                        <div class="outfit-items mb-3">
        `;

        outfit.items.forEach(item => {
            if (item.photo_filename) {
                html += `
                    <img src="/static/uploads/${item.photo_filename}"
                         class="outfit-item-thumb" alt="${item.name}" title="${item.name}">
                `;
            } else {
                html += `
                    <div class="outfit-item-thumb-placeholder" title="${item.name}">
                        <i class="fas fa-tshirt"></i>
                    </div>
                `;
            }
        });

        html += `
                        </div>

                        <!-- Outfit Description -->
                        <p class="outfit-description">${outfit.description}</p>

                        <!-- Color Palette -->
                        <div class="color-palette mb-3">
                            <div class="color-palette-label">
                                <i class="fas fa-palette me-1"></i>
                                <small>Color Palette</small>
                            </div>
                            <div class="color-circles">
                                ${generateColorPalette(outfit.items)}
                            </div>
                        </div>

                        <!-- Score Breakdown with Explanations -->
                        <div class="score-breakdown mb-3">
                            <div class="score-breakdown-header mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-chart-line me-1"></i>
                                    AI Analysis Breakdown
                                </small>
                            </div>

                            <div class="score-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="score-label">
                                        <i class="fas fa-palette me-1 text-primary"></i>
                                        <span>Style Harmony</span>
                                        <i class="fas fa-info-circle ms-1 text-muted"
                                           data-bs-toggle="tooltip"
                                           title="How well the clothing styles work together (casual, formal, etc.)"></i>
                                    </div>
                                    <div class="score-value">
                                        <span class="score-percentage">${Math.round(outfit.style_score * 100)}%</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: ${outfit.style_score * 100}%"></div>
                                        </div>
                                    </div>
                                </div>
                                ${outfit.explanations && outfit.explanations.style_score ?
                                    `<div class="score-explanation">
                                        <small class="text-muted">${outfit.explanations.style_score}</small>
                                    </div>` : ''}
                            </div>

                            <div class="score-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="score-label">
                                        <i class="fas fa-eye me-1 text-success"></i>
                                        <span>Color Harmony</span>
                                        <i class="fas fa-info-circle ms-1 text-muted"
                                           data-bs-toggle="tooltip"
                                           title="How well the colors complement each other and create visual balance"></i>
                                    </div>
                                    <div class="score-value">
                                        <span class="score-percentage">${Math.round(outfit.color_harmony * 100)}%</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: ${outfit.color_harmony * 100}%"></div>
                                        </div>
                                    </div>
                                </div>
                                ${outfit.explanations && outfit.explanations.color_harmony ?
                                    `<div class="score-explanation">
                                        <small class="text-muted">${outfit.explanations.color_harmony}</small>
                                    </div>` : ''}
                            </div>

                            <div class="score-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="score-label">
                                        <i class="fas fa-calendar-check me-1 text-warning"></i>
                                        <span>Occasion Fit</span>
                                        <i class="fas fa-info-circle ms-1 text-muted"
                                           data-bs-toggle="tooltip"
                                           title="How appropriate this outfit is for the selected occasion and weather"></i>
                                    </div>
                                    <div class="score-value">
                                        <span class="score-percentage">${Math.round(outfit.occasion_fit * 100)}%</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: ${outfit.occasion_fit * 100}%"></div>
                                        </div>
                                    </div>
                                </div>
                                ${outfit.explanations && outfit.explanations.occasion_fit ?
                                    `<div class="score-explanation">
                                        <small class="text-muted">${outfit.explanations.occasion_fit}</small>
                                    </div>` : ''}
                            </div>

                            <div class="score-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="score-label">
                                        <i class="fas fa-thermometer-half me-1 text-info"></i>
                                        <span>Weather Match</span>
                                        <i class="fas fa-info-circle ms-1 text-muted"
                                           data-bs-toggle="tooltip"
                                           title="How suitable this outfit is for the current weather conditions"></i>
                                    </div>
                                    <div class="score-value">
                                        <span class="score-percentage">${Math.round((outfit.weather_score || 0.8) * 100)}%</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: ${(outfit.weather_score || 0.8) * 100}%"></div>
                                        </div>
                                    </div>
                                </div>
                                ${outfit.explanations && outfit.explanations.versatility ?
                                    `<div class="score-explanation">
                                        <small class="text-muted">${outfit.explanations.versatility}</small>
                                    </div>` : ''}
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 mb-2">
                            <button class="btn btn-success flex-fill"
                                    onclick="saveIndividualOutfit(${index})">
                                <i class="fas fa-save me-1"></i>Save Outfit
                            </button>
                            <button class="btn btn-outline-primary"
                                    onclick="shareOutfit('${outfit.id}')"
                                    data-bs-toggle="tooltip" title="Share Outfit">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>

                        <!-- Quick Actions -->
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline-secondary btn-sm flex-fill"
                                    onclick="rateOutfit(${index})">
                                <i class="fas fa-star me-1"></i>Rate
                            </button>
                            <button class="btn btn-outline-info btn-sm flex-fill"
                                    onclick="modifyOutfit(window.outfitData[${index}])">
                                <i class="fas fa-edit me-1"></i>Modify
                            </button>
                            <button class="btn btn-outline-warning btn-sm flex-fill"
                                    onclick="tryOnOutfit(window.outfitData[${index}])">
                                <i class="fas fa-user me-1"></i>Try On
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        </div>

        <!-- Generate More Section -->
        <div class="card text-center">
            <div class="card-body">
                <h5>Want More Options?</h5>
                <p class="text-muted">Generate more outfits or try different preferences</p>
                <div class="d-flex gap-2 justify-content-center">
                    <button class="btn btn-primary regenerate-btn">
                        <i class="fas fa-redo me-2"></i>Generate More
                    </button>
                    <a href="/generate-outfit" class="btn btn-outline-info">
                        <i class="fas fa-sliders-h me-2"></i>Change Preferences
                    </a>
                    <a href="/outfit-history" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>View History
                    </a>
                </div>
            </div>
        </div>
    `;

    document.getElementById('resultsArea').innerHTML = html;
}

// Function to generate color palette from outfit items
function generateColorPalette(items) {
    const colors = new Set();
    const colorMap = {
        // Basic colors
        'black': '#000000',
        'white': '#ffffff',
        'gray': '#808080',
        'grey': '#808080',
        'red': '#dc3545',
        'blue': '#007bff',
        'green': '#28a745',
        'yellow': '#ffc107',
        'orange': '#fd7e14',
        'purple': '#6f42c1',
        'pink': '#e83e8c',
        'brown': '#8b4513',
        'beige': '#f5f5dc',
        'navy': '#000080',
        'maroon': '#800000',
        'olive': '#808000',
        'lime': '#00ff00',
        'aqua': '#00ffff',
        'teal': '#008080',
        'silver': '#c0c0c0',
        'gold': '#ffd700',
        // Extended colors
        'darkblue': '#00008b',
        'lightblue': '#add8e6',
        'darkgreen': '#006400',
        'lightgreen': '#90ee90',
        'darkred': '#8b0000',
        'lightred': '#ffcccb',
        'cream': '#fffdd0',
        'tan': '#d2b48c',
        'khaki': '#f0e68c',
        'coral': '#ff7f50',
        'salmon': '#fa8072',
        'lavender': '#e6e6fa',
        'mint': '#98fb98',
        'peach': '#ffcba4'
    };

    // Extract colors from each item
    items.forEach(item => {
        if (item.colors && Array.isArray(item.colors)) {
            item.colors.forEach(color => {
                const cleanColor = color.toLowerCase().trim();
                if (colorMap[cleanColor]) {
                    colors.add(colorMap[cleanColor]);
                } else {
                    // Try to match partial color names
                    for (const [colorName, colorValue] of Object.entries(colorMap)) {
                        if (cleanColor.includes(colorName) || colorName.includes(cleanColor)) {
                            colors.add(colorValue);
                            break;
                        }
                    }
                }
            });
        }
    });

    // If no colors found, add a default neutral palette
    if (colors.size === 0) {
        colors.add('#6c757d'); // Default gray
    }

    // Convert to array and limit to 6 colors max
    const colorArray = Array.from(colors).slice(0, 6);

    return colorArray.map(color =>
        `<div class="color-circle" style="background-color: ${color}" title="${getColorName(color)}"></div>`
    ).join('');
}

// Function to get color name from hex value
function getColorName(hex) {
    const colorNames = {
        '#000000': 'Black',
        '#ffffff': 'White',
        '#808080': 'Gray',
        '#dc3545': 'Red',
        '#007bff': 'Blue',
        '#28a745': 'Green',
        '#ffc107': 'Yellow',
        '#fd7e14': 'Orange',
        '#6f42c1': 'Purple',
        '#e83e8c': 'Pink',
        '#8b4513': 'Brown',
        '#f5f5dc': 'Beige',
        '#6c757d': 'Neutral'
    };

    return colorNames[hex] || 'Color';
}

// Function to initialize outfit button event listeners for dynamically loaded content
function initializeOutfitButtons() {
    console.log('Initializing outfit buttons...');

    // Extract outfit data from the loaded content
    const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
    console.log('Found outfit cards:', outfitCards.length);

    // Check if window.outfitData was already set
    if (!window.outfitData || window.outfitData.length === 0) {
        console.log('No outfit data found');
        return;
    } else {
        console.log('Found outfit data:', window.outfitData.length, 'items');
    }

    // Since we're using inline onclick handlers, we just need to initialize star ratings
    initializeStarRatings();

    console.log('Buttons initialized with inline onclick handlers');
}

// Functions needed for outfit results functionality
let currentOutfitData = null;
let currentOutfitIndex = null;

function saveIndividualOutfit(index) {
    console.log('saveIndividualOutfit called with index:', index);
    console.log('window.outfitData:', window.outfitData);

    // Check if outfit data exists
    if (!window.outfitData || !window.outfitData[index]) {
        console.error('No outfit data found for index:', index);
        alert('Error: Outfit data not found. Please try generating outfits again.');
        return;
    }

    const outfitData = window.outfitData[index];
    console.log('Outfit data to save:', outfitData);

    // Simple prompt-based save
    const occasion = outfitData.occasion ? outfitData.occasion.replace('_', ' ') : 'custom';
    const weather = outfitData.weather ? outfitData.weather.replace('_', ' ') : 'any';
    const defaultName = `${occasion.charAt(0).toUpperCase() + occasion.slice(1)} outfit for ${weather} weather`;

    const outfitName = prompt('Enter a name for this outfit:', defaultName);

    if (outfitName && outfitName.trim()) {
        // Prepare outfit data for saving
        const enhancedOutfitData = {
            ...outfitData,
            description: outfitName.trim(),
            notes: 'Saved from outfit generator',
            is_favorite: false,
            rating: null
        };

        console.log('Sending outfit data:', enhancedOutfitData);

        fetch('/save-outfit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(enhancedOutfitData)
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Save response:', data);
            if (data.success) {
                alert('Outfit saved successfully!');

                // Update button to show it's saved
                const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
                if (outfitCards[index]) {
                    const saveBtn = outfitCards[index].querySelector('button[onclick*="saveIndividualOutfit"]');
                    if (saveBtn) {
                        saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
                        saveBtn.disabled = true;
                        saveBtn.classList.add('btn-outline-success');
                        saveBtn.classList.remove('btn-success');
                    }
                }
            } else {
                alert('Error saving outfit: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error saving outfit:', error);
            alert('Error saving outfit. Please check the console for details.');
        });
    }
}

function shareOutfit(outfitId) {
    console.log('shareOutfit called with:', outfitId);

    // Simple share functionality
    const url = `${window.location.origin}/outfit/${outfitId}`;

    if (navigator.share) {
        // Use native sharing if available
        navigator.share({
            title: 'Check out this outfit!',
            text: 'I created this amazing outfit with my Personal Closet app!',
            url: url
        }).then(() => {
            showAlert('Outfit shared!', 'success');
        }).catch(err => {
            console.log('Error sharing:', err);
            copyToClipboard(url);
        });
    } else {
        // Fallback to clipboard
        copyToClipboard(url);
    }
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showAlert('Outfit link copied to clipboard!', 'success');
        }).catch(() => {
            showAlert('Could not copy link', 'error');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showAlert('Outfit link copied to clipboard!', 'success');
        } catch (err) {
            showAlert('Could not copy link', 'error');
        }
        document.body.removeChild(textArea);
    }
}

function rateOutfit(index) {
    console.log('rateOutfit called with index:', index);

    // Simple rating with prompt
    const rating = prompt('Rate this outfit from 1 to 5 stars:', '5');

    if (rating && !isNaN(rating)) {
        const numRating = parseInt(rating);
        if (numRating >= 1 && numRating <= 5) {
            showAlert(`You rated this outfit ${numRating} star${numRating > 1 ? 's' : ''}!`, 'success');

            // Update button to show rating
            const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
            if (outfitCards[index]) {
                const rateBtn = outfitCards[index].querySelector('button[onclick*="rateOutfit"]');
                if (rateBtn) {
                    rateBtn.innerHTML = `<i class="fas fa-star me-1"></i>${numRating}★`;
                    rateBtn.classList.add('btn-warning');
                    rateBtn.classList.remove('btn-outline-secondary');
                }
            }
        } else {
            showAlert('Please enter a rating between 1 and 5', 'error');
        }
    }
}

function modifyOutfit(outfitData) {
    console.log('modifyOutfit called with:', outfitData);

    if (confirm('Go back to outfit generator to modify preferences?')) {
        // Store the outfit data for reference
        sessionStorage.setItem('modifyOutfitData', JSON.stringify(outfitData));
        window.location.href = '/generate-outfit?modify=true';
    }
}

function tryOnOutfit(outfitData) {
    console.log('tryOnOutfit called with:', outfitData);

    // Simple try-on functionality
    if (confirm('Would you like to take a photo to try on this outfit?')) {
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            // Try to access camera
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                    showAlert('Camera access granted! (Feature coming soon)', 'success');
                    // Stop the stream for now
                    stream.getTracks().forEach(track => track.stop());
                })
                .catch(function(error) {
                    console.error('Error accessing camera:', error);
                    showAlert('Camera access denied or not available', 'error');
                });
        } else {
            showAlert('Camera not supported in this browser', 'error');
        }
    }
}

function modifySearch() {
    window.location.href = '/generate-outfit';
}

function toggleFavorite(outfitId, button) {
    console.log('Toggle favorite called for outfit:', outfitId);

    // Toggle the visual state
    const icon = button.querySelector('i');
    const isCurrentlyFavorite = icon.classList.contains('fas');

    if (isCurrentlyFavorite) {
        icon.classList.remove('fas');
        icon.classList.add('far');
        button.classList.remove('active');
    } else {
        icon.classList.remove('far');
        icon.classList.add('fas');
        button.classList.add('active');
    }

    // Here you could also send an API request to save the favorite status
    // For now, just show a message
    showAlert(isCurrentlyFavorite ? 'Removed from favorites' : 'Added to favorites', 'success');
}

function saveAllOutfits(event) {
    if (!window.outfitData || window.outfitData.length === 0) {
        showAlert('No outfits to save', 'error');
        return;
    }

    const outfits = window.outfitData;
    let savedCount = 0;
    let totalOutfits = outfits.length;

    const saveButton = event ? event.target : document.querySelector('button[onclick="saveAllOutfits()"]');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving All...';

    outfits.forEach((outfit, index) => {
        const enhancedOutfit = {
            ...outfit,
            description: `Auto-saved outfit ${index + 1}`,
            notes: 'Bulk saved from outfit generation'
        };

        fetch('/save-outfit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(enhancedOutfit)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                savedCount++;
                if (savedCount === totalOutfits) {
                    saveButton.innerHTML = '<i class="fas fa-check me-1"></i>All Saved!';
                    saveButton.classList.add('btn-outline-success');
                    saveButton.classList.remove('btn-outline-success');
                    showAlert(`All ${totalOutfits} outfits saved successfully!`, 'success');
                }
            }
        })
        .catch(error => {
            console.error('Error saving outfit:', error);
        });
    });
}

// Function to initialize star ratings (simplified)
function initializeStarRatings() {
    console.log('Star ratings initialized (using simplified prompt-based system)');
    // No complex star rating UI needed since we're using prompts
}

// Simplified functions without modals

// Additional functions for modal functionality
function saveOutfitWithDetails() {
    if (!currentOutfitData) return;

    const name = document.getElementById('outfitName').value.trim();
    const notes = document.getElementById('outfitNotes').value.trim();
    const isFavorite = document.getElementById('markAsFavorite').checked;
    const rating = document.querySelectorAll('#outfitRating i.active').length;

    if (!name) {
        showAlert('Please enter an outfit name', 'error');
        return;
    }

    // Enhance outfit data with user input
    const enhancedOutfitData = {
        ...currentOutfitData,
        description: name,
        notes: notes,
        is_favorite: isFavorite,
        rating: rating > 0 ? rating : null
    };

    const saveButton = document.querySelector('#saveOutfitModal .btn-success');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(enhancedOutfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the outfit card to show it's saved
            const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
            if (outfitCards[currentOutfitIndex]) {
                const saveBtn = outfitCards[currentOutfitIndex].querySelector('.save-outfit-btn');
                if (saveBtn) {
                    saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
                    saveBtn.disabled = true;
                    saveBtn.classList.add('btn-outline-success');
                    saveBtn.classList.remove('btn-success');
                }
            }

            bootstrap.Modal.getInstance(document.getElementById('saveOutfitModal')).hide();
            showAlert('Outfit saved successfully!', 'success');
        } else {
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving outfit', 'error');
    })
    .finally(() => {
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
    });
}

function copyOutfitLink() {
    const url = `${window.location.origin}/outfit/${window.currentOutfitId}`;
    navigator.clipboard.writeText(url).then(() => {
        showAlert('Outfit link copied to clipboard!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('shareModal')).hide();
    }).catch(() => {
        showAlert('Failed to copy link', 'error');
    });
}

function shareToWhatsApp() {
    const text = `Check out this amazing outfit I created with my Personal Closet app!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

function openCamera() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                // Create video element for camera preview
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();

                // Replace the modal content with video preview
                const tryOnModal = document.getElementById('tryOnModal');
                const modalBody = tryOnModal.querySelector('.modal-body');
                modalBody.innerHTML = `
                    <div class="text-center">
                        <video id="cameraPreview" width="300" height="200" autoplay></video>
                        <br>
                        <button class="btn btn-success mt-2" onclick="capturePhoto()">
                            <i class="fas fa-camera me-1"></i>Capture Photo
                        </button>
                        <button class="btn btn-secondary mt-2" onclick="stopCamera()">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                    </div>
                `;

                document.getElementById('cameraPreview').srcObject = stream;
                window.currentStream = stream;
            })
            .catch(function(error) {
                console.error('Error accessing camera:', error);
                showAlert('Unable to access camera. Please check permissions.', 'error');
            });
    } else {
        showAlert('Camera not supported in this browser', 'error');
    }
}

function capturePhoto() {
    const video = document.getElementById('cameraPreview');
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);

    // Convert to blob and display
    canvas.toBlob(function(blob) {
        const url = URL.createObjectURL(blob);

        // Stop camera
        stopCamera();

        // Update modal content
        const modalBody = document.getElementById('tryOnModal').querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="text-center">
                <img src="${url}" class="img-fluid rounded" style="max-height: 300px;">
                <div class="mt-3">
                    <button class="btn btn-success" onclick="saveTryOnPhoto()">
                        <i class="fas fa-save me-1"></i>Save Try-On Photo
                    </button>
                    <button class="btn btn-secondary" onclick="retakePhoto()">
                        <i class="fas fa-redo me-1"></i>Retake
                    </button>
                </div>
            </div>
        `;

        window.currentTryOnBlob = blob;
    });
}

function stopCamera() {
    if (window.currentStream) {
        window.currentStream.getTracks().forEach(track => track.stop());
        window.currentStream = null;
    }
}

function retakePhoto() {
    openCamera();
}

function saveTryOnPhoto() {
    if (window.currentTryOnBlob && currentOutfitData) {
        const formData = new FormData();
        formData.append('photo', window.currentTryOnBlob, 'try-on.jpg');
        formData.append('outfit_id', currentOutfitData.id || 'custom-outfit');

        fetch('/save-try-on-photo', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Try-on photo saved!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('tryOnModal')).hide();
            } else {
                showAlert('Error saving photo: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error saving photo', 'error');
        });
    }
}

// Auto-detect current weather (simplified)
document.addEventListener('DOMContentLoaded', function() {
    // Set default weather based on current season (simplified)
    const month = new Date().getMonth();
    let defaultWeather = 'mild';
    
    if (month >= 11 || month <= 1) {
        defaultWeather = 'cold';
    } else if (month >= 5 && month <= 8) {
        defaultWeather = 'warm';
    }
    
    if (!document.getElementById('weather').value) {
        document.getElementById('weather').value = defaultWeather;
    }

    // Handle generation mode toggle
    const autoMode = document.getElementById('auto_mode');
    const customMode = document.getElementById('custom_mode');
    const customItemSelection = document.getElementById('customItemSelection');

    function toggleGenerationMode() {
        if (customMode.checked) {
            customItemSelection.style.display = 'block';
        } else {
            customItemSelection.style.display = 'none';
        }
    }

    autoMode.addEventListener('change', toggleGenerationMode);
    customMode.addEventListener('change', toggleGenerationMode);

    // Quick preset for custom mode
    function setCustomPreset(items) {
        customMode.checked = true;
        toggleGenerationMode();

        // Clear all checkboxes first
        document.querySelectorAll('input[name="custom_items"]').forEach(cb => cb.checked = false);

        // Check specified items
        items.forEach(item => {
            const checkbox = document.querySelector(`input[value="${item}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Add quick custom presets
    window.setCustomPreset = setCustomPreset;
});
</script>
{% endblock %}
