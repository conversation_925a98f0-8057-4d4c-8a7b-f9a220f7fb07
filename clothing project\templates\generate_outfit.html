{% extends "base.html" %}

{% block title %}Generate Outfit - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-magic me-3"></i>Generate Perfect Outfit</h1>
    <p>Let AI create stylish outfits from your personal wardrobe</p>
</div>

<div class="row">
    <!-- Generation Form -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sliders-h me-2"></i>Outfit Preferences
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="outfitForm">
                    <!-- Occasion -->
                    <div class="mb-3">
                        <label for="occasion" class="form-label">Occasion *</label>
                        <select class="form-select" id="occasion" name="occasion" required>
                            <option value="">Select Occasion</option>
                            {% for occasion in occasions %}
                            <option value="{{ occasion.value }}">{{ occasion.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label for="weather" class="form-label">Weather *</label>
                        <select class="form-select" id="weather" name="weather" required>
                            <option value="">Select Weather</option>
                            {% for weather in weather_options %}
                            <option value="{{ weather.value }}">{{ weather.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Style Preference -->
                    <div class="mb-3">
                        <label for="style" class="form-label">Preferred Style</label>
                        <select class="form-select" id="style" name="style">
                            <option value="">Any Style</option>
                            {% for style in styles %}
                            <option value="{{ style.value }}">{{ style.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Leave blank to consider all styles</div>
                    </div>

                    <!-- Color Preference -->
                    <div class="mb-3">
                        <label for="color_preference" class="form-label">Color Preference</label>
                        <input type="text" class="form-control" id="color_preference" name="color_preference" 
                               placeholder="e.g., blue, red, neutral">
                        <div class="form-text">Specify a preferred color or leave blank</div>
                    </div>

                    <!-- Quick Presets -->
                    <div class="mb-4">
                        <label class="form-label">Quick Presets</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('work')">
                                <i class="fas fa-briefcase me-1"></i>Work Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('casual')">
                                <i class="fas fa-coffee me-1"></i>Casual Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('date')">
                                <i class="fas fa-heart me-1"></i>Date Night
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('formal')">
                                <i class="fas fa-star me-1"></i>Formal Event
                            </button>
                        </div>
                    </div>

                    <!-- Generation Mode -->
                    <div class="mb-3">
                        <label class="form-label">Generation Mode</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="generation_mode" id="auto_mode" value="auto" checked>
                            <label class="form-check-label" for="auto_mode">
                                <strong>Auto Generate</strong><br>
                                <small class="text-muted">Let AI create complete outfits</small>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="generation_mode" id="custom_mode" value="custom">
                            <label class="form-check-label" for="custom_mode">
                                <strong>Custom Builder</strong><br>
                                <small class="text-muted">Choose specific item types</small>
                            </label>
                        </div>
                    </div>

                    <!-- Custom Item Selection (hidden by default) -->
                    <div id="customItemSelection" class="mb-3" style="display: none;">
                        <label class="form-label">Select Item Types</label>
                        <div class="custom-items-grid">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_tops" name="custom_items" value="top">
                                <label class="form-check-label" for="include_tops">
                                    <i class="fas fa-tshirt me-1"></i>Tops
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_bottoms" name="custom_items" value="bottom">
                                <label class="form-check-label" for="include_bottoms">
                                    <i class="fas fa-user-tie me-1"></i>Bottoms
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_dresses" name="custom_items" value="dress">
                                <label class="form-check-label" for="include_dresses">
                                    <i class="fas fa-female me-1"></i>Dresses
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_shoes" name="custom_items" value="shoes">
                                <label class="form-check-label" for="include_shoes">
                                    <i class="fas fa-shoe-prints me-1"></i>Shoes
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_outerwear" name="custom_items" value="outerwear">
                                <label class="form-check-label" for="include_outerwear">
                                    <i class="fas fa-coat-arms me-1"></i>Outerwear
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_accessories" name="custom_items" value="accessories">
                                <label class="form-check-label" for="include_accessories">
                                    <i class="fas fa-gem me-1"></i>Accessories
                                </label>
                            </div>
                        </div>
                        <div class="form-text">Select at least one item type for your custom outfit</div>

                        <!-- Quick Custom Presets -->
                        <div class="mt-2">
                            <small class="text-muted">Quick presets:</small><br>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top', 'bottom'])">
                                <i class="fas fa-tshirt me-1"></i>Top + Bottom
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['dress'])">
                                <i class="fas fa-female me-1"></i>Dress Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top'])">
                                <i class="fas fa-tshirt me-1"></i>Top Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['shoes'])">
                                <i class="fas fa-shoe-prints me-1"></i>Shoes Only
                            </button>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>Generate Outfits
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Results Area -->
    <div class="col-lg-8">
        <div id="resultsArea">
            <!-- Welcome Message -->
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-tshirt fa-4x text-muted mb-4"></i>
                    <h4>Ready to Create Amazing Outfits?</h4>
                    <p class="text-muted mb-4">
                        Select your preferences on the left and let our AI stylist create perfect outfits from your personal wardrobe.
                    </p>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Smart Color Matching</h6>
                            <p class="small text-muted">AI analyzes color harmony for perfect combinations</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Style Consistency</h6>
                            <p class="small text-muted">Ensures all pieces work together stylistically</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Weather Appropriate</h6>
                            <p class="small text-muted">Considers weather conditions for practical choices</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Occasion Perfect</h6>
                            <p class="small text-muted">Matches formality level to your occasion</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.custom-items-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.custom-items-grid .form-check {
    margin-bottom: 0;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.custom-items-grid .form-check:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.custom-items-grid .form-check-input:checked + .form-check-label {
    color: var(--primary-color);
    font-weight: 500;
}

.outfit-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.outfit-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Rating Stars */
.rating-stars {
    font-size: 1.2rem;
    cursor: pointer;
}

.rating-stars i {
    color: #ddd;
    transition: color 0.2s ease;
    margin-right: 0.2rem;
}

.rating-stars i:hover,
.rating-stars i.active {
    color: #ffc107;
}

.rating-stars-large {
    font-size: 2rem;
    cursor: pointer;
}

.rating-stars-large i {
    color: #ddd;
    transition: all 0.2s ease;
    margin: 0 0.3rem;
}

.rating-stars-large i:hover,
.rating-stars-large i.active {
    color: #ffc107;
    transform: scale(1.1);
}

.outfit-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.score-breakdown {
    font-size: 0.85rem;
}

.outfit-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 15px 0;
}

.outfit-item-thumb {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.outfit-item-thumb-placeholder {
    width: 70px;
    height: 70px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #6c757d;
}

.loading-spinner {
    text-align: center;
    padding: 60px 20px;
}

.preset-btn {
    transition: all 0.2s ease;
}

.preset-btn:hover {
    transform: translateX(5px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function setPreset(type) {
    const presets = {
        'work': {
            occasion: 'work',
            weather: 'mild',
            style: 'business',
            color: ''
        },
        'casual': {
            occasion: 'casual',
            weather: 'mild',
            style: 'casual',
            color: ''
        },
        'date': {
            occasion: 'date',
            weather: 'mild',
            style: 'romantic',
            color: ''
        },
        'formal': {
            occasion: 'formal',
            weather: 'mild',
            style: 'classic',
            color: ''
        }
    };
    
    const preset = presets[type];
    if (preset) {
        document.getElementById('occasion').value = preset.occasion;
        document.getElementById('weather').value = preset.weather;
        document.getElementById('style').value = preset.style;
        document.getElementById('color_preference').value = preset.color;
    }
}

document.getElementById('outfitForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading state
    document.getElementById('resultsArea').innerHTML = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5>Creating Your Perfect Outfits...</h5>
            <p class="text-muted">Our AI stylist is analyzing your wardrobe and preferences</p>
        </div>
    `;
    
    // Submit form
    const formData = new FormData(this);
    
    // Add AJAX header to get JSON response
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.outfits) {
            // Store outfit data globally
            window.outfitData = data.outfits;
            console.log('Received outfit data:', window.outfitData);

            // Render outfits using the data
            renderOutfits(data);

            // Initialize button event listeners
            setTimeout(() => {
                initializeOutfitButtons();
            }, 100);
        } else {
            document.getElementById('resultsArea').innerHTML = `
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-search fa-4x text-muted mb-4"></i>
                        <h4>No Suitable Outfits Found</h4>
                        <p class="text-muted mb-4">We couldn't find any outfits that match your criteria.</p>
                        <button class="btn btn-primary" onclick="location.reload()">Try Different Settings</button>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('resultsArea').innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>Error Generating Outfits</h5>
                    <p class="text-muted">Please try again or check your internet connection.</p>
                    <button class="btn btn-primary" onclick="location.reload()">Try Again</button>
                </div>
            </div>
        `;
    });
});

function saveOutfitFromGeneration(outfitData, button) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(outfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
            showAlert('Outfit saved to your history!', 'success');
        } else {
            button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
            button.disabled = false;
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
        button.disabled = false;
        showAlert('Error saving outfit', 'error');
    });
}

function regenerateOutfits() {
    document.getElementById('outfitForm').dispatchEvent(new Event('submit'));
}

// Function to render outfits from JSON data
function renderOutfits(data) {
    const { outfits, occasion, weather, preferred_style, generation_mode, custom_items } = data;

    let html = `
        <!-- Action Bar -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>${outfits.length} Outfits Generated
                        </h5>
                        <small class="text-muted">
                            ${generation_mode === 'custom' && custom_items ?
                                'Custom: ' + custom_items.join(', ').replace(/([a-z])([A-Z])/g, '$1 $2') :
                                'Auto-generated complete outfits'}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2 justify-content-md-end">
                            <button class="btn btn-outline-primary regenerate-btn">
                                <i class="fas fa-redo me-1"></i>Regenerate
                            </button>
                            <button class="btn btn-outline-secondary modify-search-btn">
                                <i class="fas fa-sliders-h me-1"></i>Modify Search
                            </button>
                            <button class="btn btn-outline-success save-all-btn">
                                <i class="fas fa-save me-1"></i>Save All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outfit Results -->
        <div class="row mb-4">
    `;

    outfits.forEach((outfit, index) => {
        html += `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card outfit-card h-100">
                    <div class="position-relative">
                        <!-- Outfit Score -->
                        <div class="outfit-score">
                            ${Math.round(outfit.overall_score * 100)}%
                        </div>

                        <!-- Favorite Button -->
                        <button class="favorite-btn" onclick="toggleFavorite('${outfit.id}', this)">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>

                    <div class="card-body">
                        <!-- Outfit Items -->
                        <div class="outfit-items mb-3">
        `;

        outfit.items.forEach(item => {
            if (item.photo_filename) {
                html += `
                    <img src="/static/uploads/${item.photo_filename}"
                         class="outfit-item-thumb" alt="${item.name}" title="${item.name}">
                `;
            } else {
                html += `
                    <div class="outfit-item-thumb-placeholder" title="${item.name}">
                        <i class="fas fa-tshirt"></i>
                    </div>
                `;
            }
        });

        html += `
                        </div>

                        <!-- Outfit Description -->
                        <p class="outfit-description">${outfit.description}</p>

                        <!-- Score Breakdown -->
                        <div class="score-breakdown mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Style:</span>
                                <span>${Math.round(outfit.style_score * 100)}%</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Color:</span>
                                <span>${Math.round(outfit.color_harmony * 100)}%</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Occasion:</span>
                                <span>${Math.round(outfit.occasion_fit * 100)}%</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 mb-2">
                            <button class="btn btn-success flex-fill save-outfit-btn"
                                    data-index="${index}">
                                <i class="fas fa-save me-1"></i>Save Outfit
                            </button>
                            <button class="btn btn-outline-primary share-outfit-btn"
                                    data-outfit-id="${outfit.id}"
                                    data-bs-toggle="tooltip" title="Share Outfit">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>

                        <!-- Quick Actions -->
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline-secondary btn-sm flex-fill rate-outfit-btn"
                                    data-index="${index}">
                                <i class="fas fa-star me-1"></i>Rate
                            </button>
                            <button class="btn btn-outline-info btn-sm flex-fill modify-outfit-btn">
                                <i class="fas fa-edit me-1"></i>Modify
                            </button>
                            <button class="btn btn-outline-warning btn-sm flex-fill try-on-outfit-btn">
                                <i class="fas fa-user me-1"></i>Try On
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        </div>

        <!-- Generate More Section -->
        <div class="card text-center">
            <div class="card-body">
                <h5>Want More Options?</h5>
                <p class="text-muted">Generate more outfits or try different preferences</p>
                <div class="d-flex gap-2 justify-content-center">
                    <button class="btn btn-primary regenerate-btn">
                        <i class="fas fa-redo me-2"></i>Generate More
                    </button>
                    <a href="/generate-outfit" class="btn btn-outline-info">
                        <i class="fas fa-sliders-h me-2"></i>Change Preferences
                    </a>
                    <a href="/outfit-history" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>View History
                    </a>
                </div>
            </div>
        </div>
    `;

    document.getElementById('resultsArea').innerHTML = html;
}

// Function to initialize outfit button event listeners for dynamically loaded content
function initializeOutfitButtons() {
    console.log('Initializing outfit buttons...');

    // Extract outfit data from the loaded content
    const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
    console.log('Found outfit cards:', outfitCards.length);

    // Check if window.outfitData was already set by the script execution
    if (!window.outfitData || window.outfitData.length === 0) {
        console.log('No outfit data found, creating empty array');
        window.outfitData = new Array(outfitCards.length).fill({});
    } else {
        console.log('Found outfit data:', window.outfitData.length, 'items');
    }

    // Save outfit buttons
    const saveButtons = document.querySelectorAll('#resultsArea .save-outfit-btn');
    console.log('Found save buttons:', saveButtons.length);

    saveButtons.forEach((button, idx) => {
        console.log('Adding listener to save button', idx, 'with dataset:', button.dataset);
        button.addEventListener('click', function(e) {
            console.log('Save button clicked!', this.dataset);
            const index = parseInt(this.dataset.index) || idx;
            const outfitData = window.outfitData[index];
            console.log('Opening save modal with data:', outfitData, 'index:', index);
            openSaveModal(outfitData, index);
        });
    });

    // Share outfit buttons
    document.querySelectorAll('#resultsArea .share-outfit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const outfitId = this.dataset.outfitId;
            shareOutfit(outfitId);
        });
    });

    // Rate outfit buttons
    document.querySelectorAll('#resultsArea .rate-outfit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const index = parseInt(this.dataset.index);
            rateOutfit(index);
        });
    });

    // Modify outfit buttons
    document.querySelectorAll('#resultsArea .modify-outfit-btn').forEach((button, index) => {
        button.addEventListener('click', function() {
            const outfitData = window.outfitData[index];
            modifyOutfit(outfitData);
        });
    });

    // Try on outfit buttons
    document.querySelectorAll('#resultsArea .try-on-outfit-btn').forEach((button, index) => {
        button.addEventListener('click', function() {
            const outfitData = window.outfitData[index];
            tryOnOutfit(outfitData);
        });
    });

    // Action bar buttons
    const regenerateBtn = document.querySelector('#resultsArea .regenerate-btn');
    if (regenerateBtn) {
        regenerateBtn.addEventListener('click', regenerateOutfits);
    }

    const modifySearchBtn = document.querySelector('#resultsArea .modify-search-btn');
    if (modifySearchBtn) {
        modifySearchBtn.addEventListener('click', modifySearch);
    }

    const saveAllBtn = document.querySelector('#resultsArea .save-all-btn');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllOutfits);
    }

    // Initialize star rating functionality
    initializeStarRatings();
}

// Functions needed for outfit results functionality
let currentOutfitData = null;
let currentOutfitIndex = null;

function openSaveModal(outfitData, index) {
    console.log('openSaveModal called with:', outfitData, index);

    currentOutfitData = outfitData;
    currentOutfitIndex = index;

    // Generate default name
    const occasion = outfitData && outfitData.occasion ? outfitData.occasion.replace('_', ' ') : 'custom';
    const weather = outfitData && outfitData.weather ? outfitData.weather.replace('_', ' ') : 'any';
    const defaultName = `${occasion.charAt(0).toUpperCase() + occasion.slice(1)} outfit for ${weather} weather`;

    console.log('Generated default name:', defaultName);

    // Create and show modal if it doesn't exist
    if (!document.getElementById('saveOutfitModal')) {
        console.log('Creating save outfit modal...');
        createSaveOutfitModal();
    }

    document.getElementById('outfitName').value = defaultName;
    document.getElementById('outfitNotes').value = '';
    document.getElementById('markAsFavorite').checked = false;

    // Reset rating stars
    document.querySelectorAll('#outfitRating i').forEach(star => {
        star.className = 'far fa-star';
    });

    const modal = new bootstrap.Modal(document.getElementById('saveOutfitModal'));
    modal.show();
}

function shareOutfit(outfitId) {
    // Create and show share modal if it doesn't exist
    if (!document.getElementById('shareModal')) {
        createShareModal();
    }

    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();

    window.currentOutfitId = outfitId;
}

function rateOutfit(index) {
    currentOutfitIndex = index;

    // Create and show rate modal if it doesn't exist
    if (!document.getElementById('rateOutfitModal')) {
        createRateOutfitModal();
    }

    // Reset rating stars
    document.querySelectorAll('#quickRating i').forEach(star => {
        star.className = 'far fa-star';
    });

    const modal = new bootstrap.Modal(document.getElementById('rateOutfitModal'));
    modal.show();
}

function modifyOutfit(outfitData) {
    sessionStorage.setItem('modifyOutfitData', JSON.stringify(outfitData));
    window.location.href = '/generate-outfit?modify=true';
}

function tryOnOutfit(outfitData) {
    currentOutfitData = outfitData;

    // Create and show try-on modal if it doesn't exist
    if (!document.getElementById('tryOnModal')) {
        createTryOnModal();
    }

    const modal = new bootstrap.Modal(document.getElementById('tryOnModal'));
    modal.show();
}

function modifySearch() {
    window.location.href = '/generate-outfit';
}

function saveAllOutfits(event) {
    const outfits = window.outfitData;
    let savedCount = 0;
    let totalOutfits = outfits.length;

    const saveButton = event ? event.target : document.querySelector('.save-all-btn');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving All...';

    outfits.forEach((outfit, index) => {
        const enhancedOutfit = {
            ...outfit,
            description: `Auto-saved outfit ${index + 1}`,
            notes: 'Bulk saved from outfit generation'
        };

        fetch('/save-outfit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(enhancedOutfit)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                savedCount++;
                if (savedCount === totalOutfits) {
                    saveButton.innerHTML = '<i class="fas fa-check me-1"></i>All Saved!';
                    saveButton.classList.add('btn-outline-success');
                    saveButton.classList.remove('btn-outline-success');
                    showAlert(`All ${totalOutfits} outfits saved successfully!`, 'success');
                }
            }
        })
        .catch(error => {
            console.error('Error saving outfit:', error);
        });
    });
}

// Function to initialize star ratings
function initializeStarRatings() {
    // Handle rating stars in save modal
    document.querySelectorAll('#outfitRating i').forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            const stars = document.querySelectorAll('#outfitRating i');

            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star active';
                } else {
                    s.className = 'far fa-star';
                }
            });
        });
    });

    // Handle quick rating stars
    document.querySelectorAll('#quickRating i').forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);

            // Update visual feedback
            const stars = document.querySelectorAll('#quickRating i');
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.className = 'fas fa-star active';
                } else {
                    s.className = 'far fa-star';
                }
            });

            // Auto-close modal after rating
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('rateOutfitModal')).hide();
                showAlert(`Rated ${rating} star${rating > 1 ? 's' : ''}!`, 'success');
            }, 500);
        });
    });
}

// Functions to create modals dynamically
function createSaveOutfitModal() {
    const modalHTML = `
        <div class="modal fade" id="saveOutfitModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-save me-2"></i>Save Outfit
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="saveOutfitForm">
                            <div class="mb-3">
                                <label for="outfitName" class="form-label">Outfit Name</label>
                                <input type="text" class="form-control" id="outfitName"
                                       placeholder="e.g., Work Meeting Look, Date Night Outfit">
                                <div class="form-text">Give your outfit a memorable name</div>
                            </div>

                            <div class="mb-3">
                                <label for="outfitNotes" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" id="outfitNotes" rows="3"
                                          placeholder="Add any notes about this outfit..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="outfitRating" class="form-label">Rate this outfit</label>
                                <div class="rating-stars" id="outfitRating">
                                    <i class="far fa-star" data-rating="1"></i>
                                    <i class="far fa-star" data-rating="2"></i>
                                    <i class="far fa-star" data-rating="3"></i>
                                    <i class="far fa-star" data-rating="4"></i>
                                    <i class="far fa-star" data-rating="5"></i>
                                </div>
                                <div class="form-text">Rate from 1 to 5 stars</div>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="markAsFavorite">
                                <label class="form-check-label" for="markAsFavorite">
                                    <i class="fas fa-heart text-danger me-1"></i>Mark as favorite
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="saveOutfitWithDetails()">
                            <i class="fas fa-save me-1"></i>Save Outfit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function createShareModal() {
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-share-alt me-2"></i>Share Outfit
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Share this outfit with friends:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="copyOutfitLink()">
                                <i class="fas fa-copy me-2"></i>Copy Link
                            </button>
                            <button class="btn btn-outline-success" onclick="shareToWhatsApp()">
                                <i class="fab fa-whatsapp me-2"></i>Share to WhatsApp
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function createRateOutfitModal() {
    const modalHTML = `
        <div class="modal fade" id="rateOutfitModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-star me-2"></i>Rate Outfit
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p>How do you like this outfit?</p>
                        <div class="rating-stars-large" id="quickRating">
                            <i class="far fa-star" data-rating="1"></i>
                            <i class="far fa-star" data-rating="2"></i>
                            <i class="far fa-star" data-rating="3"></i>
                            <i class="far fa-star" data-rating="4"></i>
                            <i class="far fa-star" data-rating="5"></i>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">Click to rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function createTryOnModal() {
    const modalHTML = `
        <div class="modal fade" id="tryOnModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user me-2"></i>Try On Outfit
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Virtual Try-On</strong><br>
                            Take a photo of yourself wearing this outfit to see how it looks!
                        </div>

                        <div class="text-center">
                            <button class="btn btn-primary btn-lg" onclick="openCamera()">
                                <i class="fas fa-camera me-2"></i>Take Photo
                            </button>
                            <p class="mt-2 text-muted">Or upload an existing photo</p>
                            <input type="file" class="form-control" accept="image/*" id="tryOnPhoto">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// Additional functions for modal functionality
function saveOutfitWithDetails() {
    if (!currentOutfitData) return;

    const name = document.getElementById('outfitName').value.trim();
    const notes = document.getElementById('outfitNotes').value.trim();
    const isFavorite = document.getElementById('markAsFavorite').checked;
    const rating = document.querySelectorAll('#outfitRating i.active').length;

    if (!name) {
        showAlert('Please enter an outfit name', 'error');
        return;
    }

    // Enhance outfit data with user input
    const enhancedOutfitData = {
        ...currentOutfitData,
        description: name,
        notes: notes,
        is_favorite: isFavorite,
        rating: rating > 0 ? rating : null
    };

    const saveButton = document.querySelector('#saveOutfitModal .btn-success');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(enhancedOutfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the outfit card to show it's saved
            const outfitCards = document.querySelectorAll('#resultsArea .outfit-card');
            if (outfitCards[currentOutfitIndex]) {
                const saveBtn = outfitCards[currentOutfitIndex].querySelector('.save-outfit-btn');
                if (saveBtn) {
                    saveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
                    saveBtn.disabled = true;
                    saveBtn.classList.add('btn-outline-success');
                    saveBtn.classList.remove('btn-success');
                }
            }

            bootstrap.Modal.getInstance(document.getElementById('saveOutfitModal')).hide();
            showAlert('Outfit saved successfully!', 'success');
        } else {
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error saving outfit', 'error');
    })
    .finally(() => {
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
    });
}

function copyOutfitLink() {
    const url = `${window.location.origin}/outfit/${window.currentOutfitId}`;
    navigator.clipboard.writeText(url).then(() => {
        showAlert('Outfit link copied to clipboard!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('shareModal')).hide();
    }).catch(() => {
        showAlert('Failed to copy link', 'error');
    });
}

function shareToWhatsApp() {
    const text = `Check out this amazing outfit I created with my Personal Closet app!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
}

function openCamera() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                // Create video element for camera preview
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();

                // Replace the modal content with video preview
                const tryOnModal = document.getElementById('tryOnModal');
                const modalBody = tryOnModal.querySelector('.modal-body');
                modalBody.innerHTML = `
                    <div class="text-center">
                        <video id="cameraPreview" width="300" height="200" autoplay></video>
                        <br>
                        <button class="btn btn-success mt-2" onclick="capturePhoto()">
                            <i class="fas fa-camera me-1"></i>Capture Photo
                        </button>
                        <button class="btn btn-secondary mt-2" onclick="stopCamera()">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                    </div>
                `;

                document.getElementById('cameraPreview').srcObject = stream;
                window.currentStream = stream;
            })
            .catch(function(error) {
                console.error('Error accessing camera:', error);
                showAlert('Unable to access camera. Please check permissions.', 'error');
            });
    } else {
        showAlert('Camera not supported in this browser', 'error');
    }
}

function capturePhoto() {
    const video = document.getElementById('cameraPreview');
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0);

    // Convert to blob and display
    canvas.toBlob(function(blob) {
        const url = URL.createObjectURL(blob);

        // Stop camera
        stopCamera();

        // Update modal content
        const modalBody = document.getElementById('tryOnModal').querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="text-center">
                <img src="${url}" class="img-fluid rounded" style="max-height: 300px;">
                <div class="mt-3">
                    <button class="btn btn-success" onclick="saveTryOnPhoto()">
                        <i class="fas fa-save me-1"></i>Save Try-On Photo
                    </button>
                    <button class="btn btn-secondary" onclick="retakePhoto()">
                        <i class="fas fa-redo me-1"></i>Retake
                    </button>
                </div>
            </div>
        `;

        window.currentTryOnBlob = blob;
    });
}

function stopCamera() {
    if (window.currentStream) {
        window.currentStream.getTracks().forEach(track => track.stop());
        window.currentStream = null;
    }
}

function retakePhoto() {
    openCamera();
}

function saveTryOnPhoto() {
    if (window.currentTryOnBlob && currentOutfitData) {
        const formData = new FormData();
        formData.append('photo', window.currentTryOnBlob, 'try-on.jpg');
        formData.append('outfit_id', currentOutfitData.id || 'custom-outfit');

        fetch('/save-try-on-photo', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Try-on photo saved!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('tryOnModal')).hide();
            } else {
                showAlert('Error saving photo: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error saving photo', 'error');
        });
    }
}

// Auto-detect current weather (simplified)
document.addEventListener('DOMContentLoaded', function() {
    // Set default weather based on current season (simplified)
    const month = new Date().getMonth();
    let defaultWeather = 'mild';
    
    if (month >= 11 || month <= 1) {
        defaultWeather = 'cold';
    } else if (month >= 5 && month <= 8) {
        defaultWeather = 'warm';
    }
    
    if (!document.getElementById('weather').value) {
        document.getElementById('weather').value = defaultWeather;
    }

    // Handle generation mode toggle
    const autoMode = document.getElementById('auto_mode');
    const customMode = document.getElementById('custom_mode');
    const customItemSelection = document.getElementById('customItemSelection');

    function toggleGenerationMode() {
        if (customMode.checked) {
            customItemSelection.style.display = 'block';
        } else {
            customItemSelection.style.display = 'none';
        }
    }

    autoMode.addEventListener('change', toggleGenerationMode);
    customMode.addEventListener('change', toggleGenerationMode);

    // Quick preset for custom mode
    function setCustomPreset(items) {
        customMode.checked = true;
        toggleGenerationMode();

        // Clear all checkboxes first
        document.querySelectorAll('input[name="custom_items"]').forEach(cb => cb.checked = false);

        // Check specified items
        items.forEach(item => {
            const checkbox = document.querySelector(`input[value="${item}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Add quick custom presets
    window.setCustomPreset = setCustomPreset;
});
</script>
{% endblock %}
