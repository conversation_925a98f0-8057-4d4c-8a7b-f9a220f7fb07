{% extends "base.html" %}

{% block title %}Generate Outfit - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-magic me-3"></i>Generate Perfect Outfit</h1>
    <p>Let AI create stylish outfits from your personal wardrobe</p>
</div>

<div class="row">
    <!-- Generation Form -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sliders-h me-2"></i>Outfit Preferences
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="outfitForm">
                    <!-- Occasion -->
                    <div class="mb-3">
                        <label for="occasion" class="form-label">Occasion *</label>
                        <select class="form-select" id="occasion" name="occasion" required>
                            <option value="">Select Occasion</option>
                            {% for occasion in occasions %}
                            <option value="{{ occasion.value }}">{{ occasion.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label for="weather" class="form-label">Weather *</label>
                        <select class="form-select" id="weather" name="weather" required>
                            <option value="">Select Weather</option>
                            {% for weather in weather_options %}
                            <option value="{{ weather.value }}">{{ weather.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Style Preference -->
                    <div class="mb-3">
                        <label for="style" class="form-label">Preferred Style</label>
                        <select class="form-select" id="style" name="style">
                            <option value="">Any Style</option>
                            {% for style in styles %}
                            <option value="{{ style.value }}">{{ style.value.replace('_', ' ').title() }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Leave blank to consider all styles</div>
                    </div>

                    <!-- Color Preference -->
                    <div class="mb-3">
                        <label for="color_preference" class="form-label">Color Preference</label>
                        <input type="text" class="form-control" id="color_preference" name="color_preference" 
                               placeholder="e.g., blue, red, neutral">
                        <div class="form-text">Specify a preferred color or leave blank</div>
                    </div>

                    <!-- Quick Presets -->
                    <div class="mb-4">
                        <label class="form-label">Quick Presets</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('work')">
                                <i class="fas fa-briefcase me-1"></i>Work Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('casual')">
                                <i class="fas fa-coffee me-1"></i>Casual Day
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('date')">
                                <i class="fas fa-heart me-1"></i>Date Night
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('formal')">
                                <i class="fas fa-star me-1"></i>Formal Event
                            </button>
                        </div>
                    </div>

                    <!-- Generation Mode -->
                    <div class="mb-3">
                        <label class="form-label">Generation Mode</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="generation_mode" id="auto_mode" value="auto" checked>
                            <label class="form-check-label" for="auto_mode">
                                <strong>Auto Generate</strong><br>
                                <small class="text-muted">Let AI create complete outfits</small>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="generation_mode" id="custom_mode" value="custom">
                            <label class="form-check-label" for="custom_mode">
                                <strong>Custom Builder</strong><br>
                                <small class="text-muted">Choose specific item types</small>
                            </label>
                        </div>
                    </div>

                    <!-- Custom Item Selection (hidden by default) -->
                    <div id="customItemSelection" class="mb-3" style="display: none;">
                        <label class="form-label">Select Item Types</label>
                        <div class="custom-items-grid">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_tops" name="custom_items" value="top">
                                <label class="form-check-label" for="include_tops">
                                    <i class="fas fa-tshirt me-1"></i>Tops
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_bottoms" name="custom_items" value="bottom">
                                <label class="form-check-label" for="include_bottoms">
                                    <i class="fas fa-user-tie me-1"></i>Bottoms
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_dresses" name="custom_items" value="dress">
                                <label class="form-check-label" for="include_dresses">
                                    <i class="fas fa-female me-1"></i>Dresses
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_shoes" name="custom_items" value="shoes">
                                <label class="form-check-label" for="include_shoes">
                                    <i class="fas fa-shoe-prints me-1"></i>Shoes
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_outerwear" name="custom_items" value="outerwear">
                                <label class="form-check-label" for="include_outerwear">
                                    <i class="fas fa-coat-arms me-1"></i>Outerwear
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_accessories" name="custom_items" value="accessories">
                                <label class="form-check-label" for="include_accessories">
                                    <i class="fas fa-gem me-1"></i>Accessories
                                </label>
                            </div>
                        </div>
                        <div class="form-text">Select at least one item type for your custom outfit</div>

                        <!-- Quick Custom Presets -->
                        <div class="mt-2">
                            <small class="text-muted">Quick presets:</small><br>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top', 'bottom'])">
                                <i class="fas fa-tshirt me-1"></i>Top + Bottom
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['dress'])">
                                <i class="fas fa-female me-1"></i>Dress Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['top'])">
                                <i class="fas fa-tshirt me-1"></i>Top Only
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="setCustomPreset(['shoes'])">
                                <i class="fas fa-shoe-prints me-1"></i>Shoes Only
                            </button>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>Generate Outfits
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Results Area -->
    <div class="col-lg-8">
        <div id="resultsArea">
            <!-- Welcome Message -->
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-tshirt fa-4x text-muted mb-4"></i>
                    <h4>Ready to Create Amazing Outfits?</h4>
                    <p class="text-muted mb-4">
                        Select your preferences on the left and let our AI stylist create perfect outfits from your personal wardrobe.
                    </p>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Smart Color Matching</h6>
                            <p class="small text-muted">AI analyzes color harmony for perfect combinations</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Style Consistency</h6>
                            <p class="small text-muted">Ensures all pieces work together stylistically</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Weather Appropriate</h6>
                            <p class="small text-muted">Considers weather conditions for practical choices</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Occasion Perfect</h6>
                            <p class="small text-muted">Matches formality level to your occasion</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.custom-items-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.custom-items-grid .form-check {
    margin-bottom: 0;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.custom-items-grid .form-check:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.custom-items-grid .form-check-input:checked + .form-check-label {
    color: var(--primary-color);
    font-weight: 500;
}

.outfit-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.outfit-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.outfit-score {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.score-breakdown {
    font-size: 0.85rem;
}

.outfit-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 15px 0;
}

.outfit-item-thumb {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.outfit-item-thumb-placeholder {
    width: 70px;
    height: 70px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #6c757d;
}

.loading-spinner {
    text-align: center;
    padding: 60px 20px;
}

.preset-btn {
    transition: all 0.2s ease;
}

.preset-btn:hover {
    transform: translateX(5px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function setPreset(type) {
    const presets = {
        'work': {
            occasion: 'work',
            weather: 'mild',
            style: 'business',
            color: ''
        },
        'casual': {
            occasion: 'casual',
            weather: 'mild',
            style: 'casual',
            color: ''
        },
        'date': {
            occasion: 'date',
            weather: 'mild',
            style: 'romantic',
            color: ''
        },
        'formal': {
            occasion: 'formal',
            weather: 'mild',
            style: 'classic',
            color: ''
        }
    };
    
    const preset = presets[type];
    if (preset) {
        document.getElementById('occasion').value = preset.occasion;
        document.getElementById('weather').value = preset.weather;
        document.getElementById('style').value = preset.style;
        document.getElementById('color_preference').value = preset.color;
    }
}

document.getElementById('outfitForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading state
    document.getElementById('resultsArea').innerHTML = `
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5>Creating Your Perfect Outfits...</h5>
            <p class="text-muted">Our AI stylist is analyzing your wardrobe and preferences</p>
        </div>
    `;
    
    // Submit form
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(html => {
        // Parse the response to extract just the results
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const results = doc.querySelector('#outfitResults');
        
        if (results) {
            document.getElementById('resultsArea').innerHTML = results.innerHTML;
        } else {
            // If no results found, show the full response (likely contains error messages)
            document.getElementById('resultsArea').innerHTML = html;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('resultsArea').innerHTML = `
            <div class="card text-center">
                <div class="card-body py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>Error Generating Outfits</h5>
                    <p class="text-muted">Please try again or check your internet connection.</p>
                    <button class="btn btn-primary" onclick="location.reload()">Try Again</button>
                </div>
            </div>
        `;
    });
});

function saveOutfitFromGeneration(outfitData, button) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    fetch('/save-outfit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(outfitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Saved!';
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
            showAlert('Outfit saved to your history!', 'success');
        } else {
            button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
            button.disabled = false;
            showAlert('Error saving outfit: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-save me-1"></i>Save Outfit';
        button.disabled = false;
        showAlert('Error saving outfit', 'error');
    });
}

function regenerateOutfits() {
    document.getElementById('outfitForm').dispatchEvent(new Event('submit'));
}

// Auto-detect current weather (simplified)
document.addEventListener('DOMContentLoaded', function() {
    // Set default weather based on current season (simplified)
    const month = new Date().getMonth();
    let defaultWeather = 'mild';
    
    if (month >= 11 || month <= 1) {
        defaultWeather = 'cold';
    } else if (month >= 5 && month <= 8) {
        defaultWeather = 'warm';
    }
    
    if (!document.getElementById('weather').value) {
        document.getElementById('weather').value = defaultWeather;
    }

    // Handle generation mode toggle
    const autoMode = document.getElementById('auto_mode');
    const customMode = document.getElementById('custom_mode');
    const customItemSelection = document.getElementById('customItemSelection');

    function toggleGenerationMode() {
        if (customMode.checked) {
            customItemSelection.style.display = 'block';
        } else {
            customItemSelection.style.display = 'none';
        }
    }

    autoMode.addEventListener('change', toggleGenerationMode);
    customMode.addEventListener('change', toggleGenerationMode);

    // Quick preset for custom mode
    function setCustomPreset(items) {
        customMode.checked = true;
        toggleGenerationMode();

        // Clear all checkboxes first
        document.querySelectorAll('input[name="custom_items"]').forEach(cb => cb.checked = false);

        // Check specified items
        items.forEach(item => {
            const checkbox = document.querySelector(`input[value="${item}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Add quick custom presets
    window.setCustomPreset = setCustomPreset;
});
</script>
{% endblock %}
