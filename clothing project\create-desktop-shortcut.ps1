# PowerShell script to create a desktop shortcut for Personal Closet
param(
    [string]$DesktopPath = [Environment]::GetFolderPath("Desktop")
)

Write-Host "Creating Personal Closet Desktop Shortcut..." -ForegroundColor Green

# Get current directory
$CurrentDir = (Get-Location).Path
$LauncherPath = Join-Path $CurrentDir "launch-desktop-app.bat"
$IconPath = Join-Path $CurrentDir "assets\icon.ico"

# Create shortcut
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$DesktopPath\Personal Closet.lnk")
$Shortcut.TargetPath = $LauncherPath
$Shortcut.WorkingDirectory = $CurrentDir
$Shortcut.Description = "Personal Closet - AI-Powered Wardrobe Management"

# Set icon if it exists
if (Test-Path $IconPath) {
    $Shortcut.IconLocation = $IconPath
}

$Shortcut.Save()

Write-Host "Desktop shortcut created successfully!" -ForegroundColor Green
Write-Host "You can now double-click 'Personal Closet' on your desktop to launch the app." -ForegroundColor Yellow

# Also create Start Menu shortcut
$StartMenuPath = [Environment]::GetFolderPath("StartMenu") + "\Programs"
$StartMenuShortcut = $WshShell.CreateShortcut("$StartMenuPath\Personal Closet.lnk")
$StartMenuShortcut.TargetPath = $LauncherPath
$StartMenuShortcut.WorkingDirectory = $CurrentDir
$StartMenuShortcut.Description = "Personal Closet - AI-Powered Wardrobe Management"

if (Test-Path $IconPath) {
    $StartMenuShortcut.IconLocation = $IconPath
}

$StartMenuShortcut.Save()
Write-Host "Start Menu shortcut created successfully!" -ForegroundColor Green
