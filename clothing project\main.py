#!/usr/bin/env python3
"""
Stylish Clothing Generator - Main Application
Interactive CLI for generating stylish outfit recommendations
"""

import sys
from typing import Optional
from clothing_generator import (
    OutfitGenerator, Occasion, Weather, Style, Outfit
)


class OutfitGeneratorCLI:
    """Command-line interface for the outfit generator"""
    
    def __init__(self):
        self.generator = OutfitGenerator()
        self.current_preferences = {
            'occasion': Occasion.CASUAL,
            'weather': Weather.MILD,
            'style': None,
            'color': None
        }
    
    def run(self):
        """Main application loop"""
        print("🌟 Welcome to the Stylish Clothing Generator! 🌟")
        print("Let's find you the perfect outfit!\n")
        
        while True:
            self.show_menu()
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == '1':
                self.set_preferences()
            elif choice == '2':
                self.generate_outfits()
            elif choice == '3':
                self.quick_generate()
            elif choice == '4':
                self.show_current_preferences()
            elif choice == '5':
                self.show_help()
            elif choice == '6':
                print("👋 Thanks for using the Stylish Clothing Generator!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
            
            input("\nPress Enter to continue...")
    
    def show_menu(self):
        """Display the main menu"""
        print("\n" + "="*50)
        print("✨ STYLISH CLOTHING GENERATOR ✨")
        print("="*50)
        print("1. 🎯 Set Preferences")
        print("2. 👗 Generate Outfits")
        print("3. ⚡ Quick Generate (current preferences)")
        print("4. 📋 Show Current Preferences")
        print("5. ❓ Help")
        print("6. 🚪 Exit")
    
    def set_preferences(self):
        """Set user preferences for outfit generation"""
        print("\n🎯 Setting Your Preferences")
        print("-" * 30)
        
        # Set occasion
        print("\n📅 What's the occasion?")
        occasions = list(Occasion)
        for i, occasion in enumerate(occasions, 1):
            print(f"{i}. {occasion.value.title()}")
        
        try:
            choice = int(input(f"Choose occasion (1-{len(occasions)}): ")) - 1
            if 0 <= choice < len(occasions):
                self.current_preferences['occasion'] = occasions[choice]
                print(f"✅ Occasion set to: {occasions[choice].value.title()}")
            else:
                print("❌ Invalid choice, keeping current setting.")
        except ValueError:
            print("❌ Invalid input, keeping current setting.")
        
        # Set weather
        print("\n🌤️ What's the weather like?")
        weathers = list(Weather)
        for i, weather in enumerate(weathers, 1):
            print(f"{i}. {weather.value.title()}")
        
        try:
            choice = int(input(f"Choose weather (1-{len(weathers)}): ")) - 1
            if 0 <= choice < len(weathers):
                self.current_preferences['weather'] = weathers[choice]
                print(f"✅ Weather set to: {weathers[choice].value.title()}")
            else:
                print("❌ Invalid choice, keeping current setting.")
        except ValueError:
            print("❌ Invalid input, keeping current setting.")
        
        # Set style preference (optional)
        print("\n💫 Any style preference? (optional)")
        styles = list(Style)
        print("0. No preference")
        for i, style in enumerate(styles, 1):
            print(f"{i}. {style.value.title()}")
        
        try:
            choice = int(input(f"Choose style (0-{len(styles)}): "))
            if choice == 0:
                self.current_preferences['style'] = None
                print("✅ No style preference set")
            elif 1 <= choice <= len(styles):
                self.current_preferences['style'] = styles[choice - 1]
                print(f"✅ Style preference set to: {styles[choice - 1].value.title()}")
            else:
                print("❌ Invalid choice, keeping current setting.")
        except ValueError:
            print("❌ Invalid input, keeping current setting.")
        
        # Set color preference (optional)
        print("\n🎨 Any color preference? (optional)")
        common_colors = ['black', 'white', 'navy', 'gray', 'beige', 'brown', 'red', 'blue']
        print("0. No preference")
        for i, color in enumerate(common_colors, 1):
            print(f"{i}. {color.title()}")
        
        try:
            choice = int(input(f"Choose color (0-{len(common_colors)}) or type custom: "))
            if choice == 0:
                self.current_preferences['color'] = None
                print("✅ No color preference set")
            elif 1 <= choice <= len(common_colors):
                self.current_preferences['color'] = common_colors[choice - 1]
                print(f"✅ Color preference set to: {common_colors[choice - 1].title()}")
            else:
                print("❌ Invalid choice, keeping current setting.")
        except ValueError:
            # Allow custom color input
            custom_color = input("Enter custom color: ").strip().lower()
            if custom_color:
                self.current_preferences['color'] = custom_color
                print(f"✅ Color preference set to: {custom_color.title()}")
    
    def generate_outfits(self):
        """Generate and display outfit recommendations"""
        print("\n👗 Generating Your Perfect Outfits...")
        print("-" * 40)
        
        try:
            outfits = self.generator.generate_outfit(
                occasion=self.current_preferences['occasion'],
                weather=self.current_preferences['weather'],
                preferred_style=self.current_preferences['style'],
                color_preference=self.current_preferences['color'],
                num_suggestions=3
            )
            
            if not outfits:
                print("😔 Sorry, no suitable outfits found with your current preferences.")
                print("💡 Try adjusting your preferences or use Quick Generate.")
                return
            
            print(f"🎉 Found {len(outfits)} great outfit{'s' if len(outfits) > 1 else ''}!\n")
            
            for i, outfit in enumerate(outfits, 1):
                self.display_outfit(outfit, i)
                print()
        
        except Exception as e:
            print(f"❌ Error generating outfits: {e}")
    
    def quick_generate(self):
        """Quick outfit generation with current preferences"""
        print("\n⚡ Quick Outfit Generation")
        print("-" * 30)
        
        try:
            outfits = self.generator.generate_outfit(
                occasion=self.current_preferences['occasion'],
                weather=self.current_preferences['weather'],
                preferred_style=self.current_preferences['style'],
                color_preference=self.current_preferences['color'],
                num_suggestions=1
            )
            
            if outfits:
                print("🎯 Here's your quick outfit suggestion:\n")
                self.display_outfit(outfits[0], 1)
            else:
                print("😔 No suitable outfits found. Try adjusting your preferences.")
        
        except Exception as e:
            print(f"❌ Error generating outfit: {e}")
    
    def display_outfit(self, outfit: Outfit, number: int):
        """Display a single outfit with styling"""
        print(f"🌟 OUTFIT #{number}")
        print("=" * 20)
        print(f"📝 {outfit.description}")
        print(f"⭐ Overall Score: {outfit.overall_score:.1%}")
        print(f"🎨 Color Harmony: {outfit.color_harmony:.1%}")
        print(f"💫 Style Score: {outfit.style_score:.1%}")
        print(f"🎯 Occasion Fit: {outfit.occasion_fit:.1%}")
        
        print("\n👕 Items:")
        for item in outfit.items:
            colors_str = ", ".join(item.colors)
            print(f"  • {item.name} ({colors_str})")
        
        # Add styling tips
        if outfit.overall_score >= 0.8:
            print("💎 This is an excellent choice!")
        elif outfit.overall_score >= 0.7:
            print("✨ This looks great!")
        elif outfit.overall_score >= 0.6:
            print("👍 This is a solid choice!")
    
    def show_current_preferences(self):
        """Display current user preferences"""
        print("\n📋 Current Preferences")
        print("-" * 25)
        print(f"📅 Occasion: {self.current_preferences['occasion'].value.title()}")
        print(f"🌤️ Weather: {self.current_preferences['weather'].value.title()}")
        
        style = self.current_preferences['style']
        print(f"💫 Style: {style.value.title() if style else 'No preference'}")
        
        color = self.current_preferences['color']
        print(f"🎨 Color: {color.title() if color else 'No preference'}")
    
    def show_help(self):
        """Display help information"""
        print("\n❓ Help & Tips")
        print("-" * 15)
        print("🎯 Set Preferences: Configure occasion, weather, style, and color preferences")
        print("👗 Generate Outfits: Get 3 personalized outfit recommendations")
        print("⚡ Quick Generate: Get 1 quick outfit with current settings")
        print("📋 Show Preferences: View your current settings")
        print()
        print("💡 Tips:")
        print("  • Start by setting your occasion and weather")
        print("  • Style and color preferences are optional but help personalize results")
        print("  • Try different combinations to discover new looks")
        print("  • Higher scores indicate better style coordination")


def main():
    """Main entry point"""
    try:
        app = OutfitGeneratorCLI()
        app.run()
    except KeyboardInterrupt:
        print("\n\n👋 Thanks for using the Stylish Clothing Generator!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
