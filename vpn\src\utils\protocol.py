"""
VPN Protocol definitions and packet handling
"""

import struct
from enum import IntEnum
from typing import <PERSON><PERSON><PERSON><PERSON>, Optional, Union
from dataclasses import dataclass
from .logger import get_logger

logger = get_logger(__name__)

class PacketType(IntEnum):
    """VPN packet types"""
    HANDSHAKE_INIT = 1
    HANDSHAKE_RESPONSE = 2
    HANDSHAKE_COMPLETE = 3
    DATA = 4
    KEEPALIVE = 5
    DISCONNECT = 6
    AUTH_REQUEST = 7
    AUTH_RESPONSE = 8
    CONFIG_PUSH = 9
    CONFIG_ACK = 10

class AuthResult(IntEnum):
    """Authentication results"""
    SUCCESS = 0
    INVALID_CREDENTIALS = 1
    CERTIFICATE_ERROR = 2
    TIMEOUT = 3
    UNKNOWN_ERROR = 4

@dataclass
class VPNPacket:
    """VPN packet structure"""
    packet_type: PacketType
    session_id: int
    sequence: int
    payload: bytes
    
    def to_bytes(self) -> bytes:
        """Convert packet to bytes for transmission"""
        header = struct.pack('!BIH', self.packet_type, self.session_id, self.sequence)
        payload_len = struct.pack('!H', len(self.payload))
        return header + payload_len + self.payload
    
    @classmethod
    def from_bytes(cls, data: bytes) -> Optional['VPNPacket']:
        """Create packet from bytes"""
        try:
            if len(data) < 9:  # Minimum header size
                return None
                
            packet_type, session_id, sequence = struct.unpack('!BIH', data[:7])
            payload_len, = struct.unpack('!H', data[7:9])
            
            if len(data) < 9 + payload_len:
                return None
                
            payload = data[9:9 + payload_len]
            
            return cls(
                packet_type=PacketType(packet_type),
                session_id=session_id,
                sequence=sequence,
                payload=payload
            )
        except (struct.error, ValueError) as e:
            logger.warning("Failed to parse packet", error=str(e))
            return None

@dataclass
class HandshakeData:
    """Handshake packet data"""
    client_version: str
    supported_ciphers: list
    client_cert: bytes
    
    def to_bytes(self) -> bytes:
        """Convert to bytes"""
        version_bytes = self.client_version.encode('utf-8')
        ciphers_str = ','.join(self.supported_ciphers)
        ciphers_bytes = ciphers_str.encode('utf-8')
        
        data = struct.pack('!H', len(version_bytes)) + version_bytes
        data += struct.pack('!H', len(ciphers_bytes)) + ciphers_bytes
        data += struct.pack('!H', len(self.client_cert)) + self.client_cert
        
        return data
    
    @classmethod
    def from_bytes(cls, data: bytes) -> Optional['HandshakeData']:
        """Create from bytes"""
        try:
            offset = 0
            
            # Version
            version_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            version = data[offset:offset+version_len].decode('utf-8')
            offset += version_len
            
            # Ciphers
            ciphers_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            ciphers_str = data[offset:offset+ciphers_len].decode('utf-8')
            ciphers = ciphers_str.split(',') if ciphers_str else []
            offset += ciphers_len
            
            # Certificate
            cert_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            cert = data[offset:offset+cert_len]
            
            return cls(
                client_version=version,
                supported_ciphers=ciphers,
                client_cert=cert
            )
        except (struct.error, UnicodeDecodeError, IndexError) as e:
            logger.warning("Failed to parse handshake data", error=str(e))
            return None

@dataclass
class ConfigData:
    """Configuration data to push to client"""
    assigned_ip: str
    subnet_mask: str
    gateway: str
    dns_servers: list
    routes: list
    
    def to_bytes(self) -> bytes:
        """Convert to bytes"""
        data = b''
        
        # IP configuration
        for item in [self.assigned_ip, self.subnet_mask, self.gateway]:
            item_bytes = item.encode('utf-8')
            data += struct.pack('!H', len(item_bytes)) + item_bytes
        
        # DNS servers
        dns_str = ','.join(self.dns_servers)
        dns_bytes = dns_str.encode('utf-8')
        data += struct.pack('!H', len(dns_bytes)) + dns_bytes
        
        # Routes
        routes_str = ';'.join([f"{r['network']},{r['netmask']},{r['gateway']}" for r in self.routes])
        routes_bytes = routes_str.encode('utf-8')
        data += struct.pack('!H', len(routes_bytes)) + routes_bytes
        
        return data
    
    @classmethod
    def from_bytes(cls, data: bytes) -> Optional['ConfigData']:
        """Create from bytes"""
        try:
            offset = 0
            
            # IP, mask, gateway
            ip_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            assigned_ip = data[offset:offset+ip_len].decode('utf-8')
            offset += ip_len
            
            mask_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            subnet_mask = data[offset:offset+mask_len].decode('utf-8')
            offset += mask_len
            
            gw_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            gateway = data[offset:offset+gw_len].decode('utf-8')
            offset += gw_len
            
            # DNS servers
            dns_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            dns_str = data[offset:offset+dns_len].decode('utf-8')
            dns_servers = dns_str.split(',') if dns_str else []
            offset += dns_len
            
            # Routes
            routes_len, = struct.unpack('!H', data[offset:offset+2])
            offset += 2
            routes_str = data[offset:offset+routes_len].decode('utf-8')
            
            routes = []
            if routes_str:
                for route_str in routes_str.split(';'):
                    parts = route_str.split(',')
                    if len(parts) == 3:
                        routes.append({
                            'network': parts[0],
                            'netmask': parts[1],
                            'gateway': parts[2]
                        })
            
            return cls(
                assigned_ip=assigned_ip,
                subnet_mask=subnet_mask,
                gateway=gateway,
                dns_servers=dns_servers,
                routes=routes
            )
        except (struct.error, UnicodeDecodeError, IndexError) as e:
            logger.warning("Failed to parse config data", error=str(e))
            return None

class ProtocolHandler:
    """Handles VPN protocol operations"""
    
    def __init__(self):
        self.sequence_counter = 0
    
    def create_packet(self, packet_type: PacketType, session_id: int, payload: bytes) -> VPNPacket:
        """Create a new VPN packet"""
        self.sequence_counter += 1
        return VPNPacket(
            packet_type=packet_type,
            session_id=session_id,
            sequence=self.sequence_counter,
            payload=payload
        )
    
    def create_handshake_init(self, session_id: int, handshake_data: HandshakeData) -> VPNPacket:
        """Create handshake initiation packet"""
        return self.create_packet(PacketType.HANDSHAKE_INIT, session_id, handshake_data.to_bytes())
    
    def create_config_push(self, session_id: int, config_data: ConfigData) -> VPNPacket:
        """Create configuration push packet"""
        return self.create_packet(PacketType.CONFIG_PUSH, session_id, config_data.to_bytes())
    
    def create_keepalive(self, session_id: int) -> VPNPacket:
        """Create keepalive packet"""
        return self.create_packet(PacketType.KEEPALIVE, session_id, b'')
    
    def create_data_packet(self, session_id: int, data: bytes) -> VPNPacket:
        """Create data packet"""
        return self.create_packet(PacketType.DATA, session_id, data)
