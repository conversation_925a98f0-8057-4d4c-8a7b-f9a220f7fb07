version: '3.8'

services:
  # Privacy VPN Server
  privacy-vpn:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: privacy-vpn-server
    restart: unless-stopped
    
    # Network configuration
    network_mode: host
    
    # Required for VPN functionality
    privileged: true
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    devices:
      - /dev/net/tun:/dev/net/tun
    
    # Environment variables
    environment:
      - VPN_SERVER_HOST=0.0.0.0
      - VPN_SERVER_PORT=1194
      - VPN_PROTOCOL=udp
      - PRIVACY_MODE=true
      - DNS_BLOCKING=true
      - LOG_LEVEL=INFO
    
    # Volumes
    volumes:
      - vpn-config:/etc/vpn
      - vpn-logs:/var/log/vpn
      - vpn-certs:/etc/vpn/certs
      - ./docker/privacy-config.yaml:/etc/vpn/privacy-config.yaml:ro
    
    # Ports
    ports:
      - "1194:1194/udp"
      - "1194:1194/tcp"
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "cli/server_cli.py", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Web Management Interface (Optional)
  vpn-web:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: privacy-vpn-web
    restart: unless-stopped
    
    depends_on:
      - privacy-vpn
    
    environment:
      - VPN_SERVER_HOST=privacy-vpn
      - VPN_SERVER_PORT=1194
      - WEB_PORT=8080
    
    ports:
      - "8080:8080"
    
    volumes:
      - vpn-config:/etc/vpn:ro
      - vpn-logs:/var/log/vpn:ro

  # Monitoring and Analytics
  vpn-monitor:
    image: grafana/grafana:latest
    container_name: privacy-vpn-monitor
    restart: unless-stopped
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=vpnadmin
      - GF_USERS_ALLOW_SIGN_UP=false
    
    ports:
      - "3000:3000"
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana-dashboard.json:/etc/grafana/provisioning/dashboards/vpn-dashboard.json:ro

# Named volumes
volumes:
  vpn-config:
    driver: local
  vpn-logs:
    driver: local
  vpn-certs:
    driver: local
  grafana-data:
    driver: local

# Networks
networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
