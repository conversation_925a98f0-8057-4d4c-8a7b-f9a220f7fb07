"""
Advanced Features for the Clothing Generator
Includes weather intelligence, seasonal recommendations, and outfit rating system
"""

import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from clothing_generator import Weather, Occasion, Style, ClothingItem, Outfit


@dataclass
class WeatherCondition:
    temperature: int  # in Celsius
    humidity: int  # percentage
    wind_speed: int  # km/h
    precipitation: bool
    season: str


class WeatherIntelligence:
    """Advanced weather-based outfit recommendations"""
    
    TEMPERATURE_RANGES = {
        Weather.COLD: (-10, 5),
        Weather.COOL: (5, 15),
        Weather.MILD: (15, 22),
        Weather.WARM: (22, 28),
        Weather.HOT: (28, 40)
    }
    
    @classmethod
    def determine_weather_category(cls, condition: WeatherCondition) -> Weather:
        """Determine weather category from detailed conditions"""
        temp = condition.temperature
        
        # Adjust for wind chill and humidity
        if condition.wind_speed > 20:
            temp -= 2  # Wind makes it feel cooler
        
        if condition.humidity > 80:
            if temp > 25:
                temp += 3  # High humidity makes heat feel worse
            elif temp < 10:
                temp -= 2  # High humidity makes cold feel worse
        
        # Check precipitation
        if condition.precipitation:
            return Weather.RAINY
        
        # Determine category based on adjusted temperature
        for weather, (min_temp, max_temp) in cls.TEMPERATURE_RANGES.items():
            if min_temp <= temp <= max_temp:
                return weather
        
        return Weather.COLD if temp < -10 else Weather.HOT
    
    @classmethod
    def get_weather_recommendations(cls, weather: Weather) -> Dict[str, List[str]]:
        """Get specific recommendations for weather conditions"""
        recommendations = {
            Weather.HOT: {
                'fabrics': ['cotton', 'linen', 'bamboo', 'moisture-wicking'],
                'colors': ['white', 'light colors', 'pastels'],
                'avoid': ['dark colors', 'heavy fabrics', 'layers'],
                'tips': ['Choose breathable fabrics', 'Opt for loose fits', 'Protect from sun']
            },
            Weather.WARM: {
                'fabrics': ['cotton', 'light wool', 'silk'],
                'colors': ['any color works', 'bright colors'],
                'avoid': ['heavy coats', 'thick sweaters'],
                'tips': ['Light layers work well', 'Perfect for most styles']
            },
            Weather.MILD: {
                'fabrics': ['cotton', 'wool', 'denim', 'light knits'],
                'colors': ['any color', 'earth tones'],
                'avoid': ['very heavy items'],
                'tips': ['Great for layering', 'Most versatile weather']
            },
            Weather.COOL: {
                'fabrics': ['wool', 'cashmere', 'denim', 'knits'],
                'colors': ['darker colors', 'jewel tones'],
                'avoid': ['very light fabrics'],
                'tips': ['Perfect for sweaters', 'Layer with jackets']
            },
            Weather.COLD: {
                'fabrics': ['wool', 'cashmere', 'fleece', 'down'],
                'colors': ['dark colors', 'rich tones'],
                'avoid': ['light fabrics', 'open shoes'],
                'tips': ['Layer effectively', 'Cover extremities', 'Insulation is key']
            },
            Weather.RAINY: {
                'fabrics': ['water-resistant', 'quick-dry', 'synthetic'],
                'colors': ['darker colors hide stains'],
                'avoid': ['suede', 'light colors', 'delicate fabrics'],
                'tips': ['Waterproof outerwear essential', 'Closed shoes', 'Quick-dry materials']
            }
        }
        return recommendations.get(weather, {})


class SeasonalRecommendations:
    """Seasonal fashion recommendations and trends"""
    
    SEASONAL_COLORS = {
        'spring': ['pastels', 'mint', 'coral', 'lavender', 'soft yellow'],
        'summer': ['bright colors', 'white', 'turquoise', 'hot pink', 'lime'],
        'autumn': ['earth tones', 'burgundy', 'rust', 'olive', 'burnt orange'],
        'winter': ['deep colors', 'navy', 'black', 'emerald', 'burgundy']
    }
    
    SEASONAL_STYLES = {
        'spring': ['romantic', 'fresh', 'floral patterns', 'light layers'],
        'summer': ['casual', 'bohemian', 'minimalist', 'beach-ready'],
        'autumn': ['classic', 'cozy', 'layered', 'textured'],
        'winter': ['elegant', 'sophisticated', 'warm layers', 'rich textures']
    }
    
    @classmethod
    def get_current_season(cls) -> str:
        """Determine current season based on date"""
        month = datetime.datetime.now().month
        
        if month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        elif month in [9, 10, 11]:
            return 'autumn'
        else:
            return 'winter'
    
    @classmethod
    def get_seasonal_recommendations(cls, season: Optional[str] = None) -> Dict[str, List[str]]:
        """Get recommendations for a specific season"""
        if season is None:
            season = cls.get_current_season()
        
        return {
            'colors': cls.SEASONAL_COLORS.get(season, []),
            'styles': cls.SEASONAL_STYLES.get(season, []),
            'season': season
        }


class OutfitRatingSystem:
    """Advanced outfit rating and feedback system"""
    
    RATING_CRITERIA = {
        'color_harmony': 0.25,
        'style_consistency': 0.25,
        'occasion_appropriateness': 0.20,
        'weather_suitability': 0.15,
        'versatility': 0.10,
        'trend_factor': 0.05
    }
    
    @classmethod
    def rate_outfit_detailed(cls, outfit: Outfit, weather: Weather, 
                           occasion: Occasion, season: str) -> Dict[str, float]:
        """Provide detailed rating breakdown"""
        ratings = {}
        
        # Color harmony (already calculated)
        ratings['color_harmony'] = outfit.color_harmony
        
        # Style consistency (already calculated)
        ratings['style_consistency'] = outfit.style_score
        
        # Occasion appropriateness (already calculated)
        ratings['occasion_appropriateness'] = outfit.occasion_fit
        
        # Weather suitability
        ratings['weather_suitability'] = cls._rate_weather_suitability(outfit.items, weather)
        
        # Versatility
        ratings['versatility'] = cls._rate_versatility(outfit.items)
        
        # Trend factor (seasonal appropriateness)
        ratings['trend_factor'] = cls._rate_trend_factor(outfit.items, season)
        
        return ratings
    
    @classmethod
    def _rate_weather_suitability(cls, items: List[ClothingItem], weather: Weather) -> float:
        """Rate how suitable the outfit is for the weather"""
        suitable_items = sum(1 for item in items if weather in item.weather)
        return suitable_items / len(items) if items else 0.0
    
    @classmethod
    def _rate_versatility(cls, items: List[ClothingItem]) -> float:
        """Rate the versatility of the outfit"""
        if not items:
            return 0.0
        
        total_versatility = sum(item.versatility for item in items)
        max_possible = len(items) * 10  # Max versatility is 10
        return total_versatility / max_possible
    
    @classmethod
    def _rate_trend_factor(cls, items: List[ClothingItem], season: str) -> float:
        """Rate how trendy/seasonally appropriate the outfit is"""
        seasonal_recs = SeasonalRecommendations.get_seasonal_recommendations(season)
        seasonal_colors = [color.lower() for color in seasonal_recs['colors']]
        
        # Check if outfit colors match seasonal recommendations
        outfit_colors = []
        for item in items:
            outfit_colors.extend([color.lower() for color in item.colors])
        
        matching_colors = sum(1 for color in outfit_colors if color in seasonal_colors)
        total_colors = len(outfit_colors) if outfit_colors else 1
        
        return matching_colors / total_colors
    
    @classmethod
    def calculate_weighted_score(cls, detailed_ratings: Dict[str, float]) -> float:
        """Calculate weighted overall score"""
        total_score = 0.0
        for criterion, weight in cls.RATING_CRITERIA.items():
            total_score += detailed_ratings.get(criterion, 0.0) * weight
        
        return min(1.0, total_score)
    
    @classmethod
    def get_improvement_suggestions(cls, detailed_ratings: Dict[str, float]) -> List[str]:
        """Provide suggestions for outfit improvement"""
        suggestions = []
        
        if detailed_ratings.get('color_harmony', 0) < 0.6:
            suggestions.append("Consider choosing colors that complement each other better")
        
        if detailed_ratings.get('style_consistency', 0) < 0.6:
            suggestions.append("Try to maintain a consistent style theme throughout the outfit")
        
        if detailed_ratings.get('weather_suitability', 0) < 0.7:
            suggestions.append("Some items might not be suitable for the current weather")
        
        if detailed_ratings.get('versatility', 0) < 0.5:
            suggestions.append("Consider adding more versatile pieces that work in multiple settings")
        
        if detailed_ratings.get('trend_factor', 0) < 0.4:
            suggestions.append("Try incorporating some seasonal colors or trends")
        
        return suggestions


class StylePersonalizer:
    """Personalized style recommendations based on user history"""
    
    def __init__(self):
        self.user_preferences = {}
        self.outfit_history = []
        self.feedback_history = []
    
    def learn_from_feedback(self, outfit: Outfit, rating: int, feedback: str):
        """Learn from user feedback to improve recommendations"""
        self.feedback_history.append({
            'outfit': outfit,
            'rating': rating,
            'feedback': feedback,
            'timestamp': datetime.datetime.now()
        })
        
        # Update preferences based on highly rated outfits
        if rating >= 4:  # 4-5 star rating
            self._update_preferences_from_positive_feedback(outfit)
    
    def _update_preferences_from_positive_feedback(self, outfit: Outfit):
        """Update user preferences based on positive feedback"""
        # Track preferred colors
        colors = []
        for item in outfit.items:
            colors.extend(item.colors)
        
        for color in colors:
            if color not in self.user_preferences:
                self.user_preferences[color] = 0
            self.user_preferences[color] += 1
        
        # Track preferred styles
        styles = set()
        for item in outfit.items:
            styles.update(item.style)
        
        for style in styles:
            style_key = f"style_{style.value}"
            if style_key not in self.user_preferences:
                self.user_preferences[style_key] = 0
            self.user_preferences[style_key] += 1
    
    def get_personalized_recommendations(self) -> Dict[str, any]:
        """Get personalized recommendations based on user history"""
        if not self.user_preferences:
            return {}
        
        # Find most preferred colors
        color_prefs = {k: v for k, v in self.user_preferences.items() 
                      if not k.startswith('style_')}
        preferred_colors = sorted(color_prefs.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Find most preferred styles
        style_prefs = {k.replace('style_', ''): v for k, v in self.user_preferences.items() 
                      if k.startswith('style_')}
        preferred_styles = sorted(style_prefs.items(), key=lambda x: x[1], reverse=True)[:2]
        
        return {
            'preferred_colors': [color for color, _ in preferred_colors],
            'preferred_styles': [style for style, _ in preferred_styles],
            'total_outfits_rated': len(self.feedback_history)
        }
