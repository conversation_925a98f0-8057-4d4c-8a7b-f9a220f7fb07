#!/bin/bash
set -e

echo "🚀 Deploying Privacy VPN Server with Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs certs config

# Set permissions
chmod +x docker/entrypoint.sh
chmod +x docker/privacy_monitor.py

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting Privacy VPN services..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

# Display connection information
echo ""
echo "✅ Privacy VPN Server deployed successfully!"
echo ""
echo "📊 Web Management Interface: http://localhost:8080"
echo "📈 Monitoring Dashboard: http://localhost:3000 (admin/vpnadmin)"
echo "🔌 VPN Server: localhost:1194 (UDP)"
echo ""
echo "🔐 To connect clients:"
echo "1. Generate client certificates:"
echo "   docker-compose exec privacy-vpn python cli/server_cli.py generate-certs --client"
echo ""
echo "2. Download client configuration:"
echo "   docker-compose exec privacy-vpn cat /etc/vpn/client.ovpn"
echo ""
echo "📋 Useful commands:"
echo "  View logs: docker-compose logs -f"
echo "  Stop services: docker-compose down"
echo "  Restart: docker-compose restart"
echo "  Update: docker-compose pull && docker-compose up -d"
echo ""
echo "🛡️  Privacy Features Enabled:"
echo "  ✅ DNS-over-HTTPS"
echo "  ✅ Ad & Tracker Blocking"
echo "  ✅ Traffic Encryption (AES-256)"
echo "  ✅ No-Logs Policy"
echo "  ✅ Firewall Protection"
echo ""

# Check if VPN is accessible
if curl -s http://localhost:8080/health > /dev/null; then
    echo "🌐 Web interface is accessible"
else
    echo "⚠️  Web interface may not be ready yet. Please wait a moment and try again."
fi

echo ""
echo "🎉 Deployment complete! Your privacy VPN is ready to use."
