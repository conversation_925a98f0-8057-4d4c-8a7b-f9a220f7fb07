#!/bin/bash
# Personal Closet Website - Docker Launcher for Linux/Mac
# Easy startup script using Docker

echo ""
echo "========================================"
echo "👗 Personal Closet Website (Docker)"
echo "========================================"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    echo "Please install Docker from: https://docs.docker.com/get-docker/"
    exit 1
fi

echo "✅ Docker is available"

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running"
    echo "Please start Docker and try again"
    exit 1
fi

echo "✅ Docker is running"

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed"
    echo "Please install Docker Compose from: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker Compose is available"

# Create data directory if it doesn't exist
mkdir -p data
mkdir -p static/uploads

echo ""
echo "🐳 Building and starting Personal Closet Website..."
echo "📱 The website will be available at: http://localhost:5000"
echo "⏹️  Press Ctrl+C to stop the application"
echo ""

# Build and run with docker-compose
docker-compose up --build

echo ""
echo "👋 Personal Closet Website stopped"
