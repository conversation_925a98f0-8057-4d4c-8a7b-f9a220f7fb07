{% extends "base.html" %}

{% block title %}My Closet - Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-tshirt me-3"></i>My Closet</h1>
    <p>Browse and manage your personal wardrobe collection</p>
</div>

<!-- Filter and Search -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="categoryFilter" class="form-label">Filter by Category</label>
                        <select class="form-select" id="categoryFilter" onchange="filterByCategory()">
                            <option value="all" {{ 'selected' if current_category == 'all' else '' }}>All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category }}" {{ 'selected' if current_category == category else '' }}>
                                {{ category.replace('_', ' ').title() }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="searchInput" class="form-label">Search Items</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search by name, brand, color..." onkeyup="searchItems()">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">Total Items</h6>
                <div class="stats-number text-primary" id="itemCount">{{ items|length }}</div>
                <a href="{{ url_for('add_item') }}" class="btn btn-primary btn-sm mt-2">
                    <i class="fas fa-plus me-1"></i>Add New Item
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Clothing Items Grid -->
{% if items %}
<div class="row" id="itemsGrid">
    {% for item in items %}
    <div class="col-lg-3 col-md-4 col-sm-6 mb-4 item-card" 
         data-name="{{ item.name.lower() }}" 
         data-brand="{{ item.brand.lower() }}" 
         data-colors="{{ item.colors|join(' ')|lower }}"
         data-category="{{ item.category }}">
        <div class="card clothing-item-card h-100">
            <!-- Item Image -->
            <div class="position-relative">
                {% if item.photo_filename %}
                <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                     class="clothing-item-image" alt="{{ item.name }}">
                {% else %}
                <div class="clothing-item-placeholder">
                    <i class="fas fa-tshirt"></i>
                </div>
                {% endif %}
                
                <!-- Condition Badge -->
                <span class="badge position-absolute top-0 start-0 m-2 
                           {{ 'bg-success' if item.condition == 'excellent' else 
                              'bg-primary' if item.condition == 'good' else 
                              'bg-warning' if item.condition == 'fair' else 'bg-danger' }}">
                    {{ item.condition.title() }}
                </span>
                
                <!-- Favorite Button -->
                <button class="favorite-btn {{ 'active' if item.is_favorite else '' }}" 
                        onclick="toggleItemFavorite('{{ item.id }}', this)">
                    <i class="fa{{ 's' if item.is_favorite else 'r' }} fa-heart"></i>
                </button>
            </div>
            
            <!-- Item Details -->
            <div class="card-body">
                <h6 class="card-title">{{ item.name }}</h6>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-tag me-1"></i>{{ item.category.replace('_', ' ').title() }}
                    </small>
                    {% if item.brand %}
                    <br><small class="text-muted">
                        <i class="fas fa-copyright me-1"></i>{{ item.brand }}
                    </small>
                    {% endif %}
                </div>
                
                <!-- Colors -->
                <div class="mb-2">
                    {% for color in item.colors %}
                    <span class="badge bg-secondary me-1">{{ color }}</span>
                    {% endfor %}
                </div>
                
                <!-- Wear Count -->
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-eye me-1"></i>Worn {{ item.wear_count }} times
                    </small>
                    {% if item.last_worn %}
                    <br><small class="text-muted">
                        <i class="fas fa-clock me-1"></i>Last worn: {{ item.last_worn[:10] }}
                    </small>
                    {% endif %}
                </div>
                
                <!-- Styles and Occasions -->
                <div class="mb-3">
                    {% for style in item.style[:2] %}
                    <span class="badge bg-light text-dark me-1">{{ style.value.title() }}</span>
                    {% endfor %}
                    {% if item.style|length > 2 %}
                    <span class="badge bg-light text-dark">+{{ item.style|length - 2 }} more</span>
                    {% endif %}
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    <a href="{{ url_for('view_item', item_id=item.id) }}" class="btn btn-outline-primary btn-sm flex-fill">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                    <a href="{{ url_for('edit_item', item_id=item.id) }}" class="btn btn-outline-secondary btn-sm flex-fill">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                    <button class="btn btn-outline-danger btn-sm" onclick="confirmDelete('{{ item.id }}', '{{ item.name }}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- No Results Message -->
<div id="noResults" class="text-center py-5" style="display: none;">
    <i class="fas fa-search fa-3x text-muted mb-3"></i>
    <h5>No Items Found</h5>
    <p class="text-muted">Try adjusting your search or filter criteria.</p>
</div>

{% else %}
<!-- Empty Closet -->
<div class="text-center py-5">
    <i class="fas fa-tshirt fa-4x text-muted mb-4"></i>
    <h4>Your Closet is Empty</h4>
    <p class="text-muted mb-4">Start building your digital wardrobe by adding your first clothing item!</p>
    <a href="{{ url_for('add_item') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>Add Your First Item
    </a>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteItemName"></strong>?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Item
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allItems = document.querySelectorAll('.item-card');

function filterByCategory() {
    const category = document.getElementById('categoryFilter').value;
    const url = new URL(window.location);
    url.searchParams.set('category', category);
    window.location.href = url.toString();
}

function searchItems() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    let visibleCount = 0;
    
    allItems.forEach(item => {
        const name = item.dataset.name;
        const brand = item.dataset.brand;
        const colors = item.dataset.colors;
        
        const matches = name.includes(searchTerm) || 
                       brand.includes(searchTerm) || 
                       colors.includes(searchTerm);
        
        if (matches) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // Update item count
    document.getElementById('itemCount').textContent = visibleCount;
    
    // Show/hide no results message
    const noResults = document.getElementById('noResults');
    if (visibleCount === 0 && allItems.length > 0) {
        noResults.style.display = 'block';
    } else {
        noResults.style.display = 'none';
    }
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    searchItems();
}

function confirmDelete(itemId, itemName) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = `/delete-item/${itemId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function toggleItemFavorite(itemId, element) {
    // This would need to be implemented in the backend
    fetch(`/toggle-item-favorite/${itemId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            element.classList.toggle('active', data.is_favorite);
            element.innerHTML = data.is_favorite ? '<i class="fas fa-heart"></i>' : '<i class="far fa-heart"></i>';
        }
    })
    .catch(error => console.error('Error:', error));
}

// Initialize search on page load
document.addEventListener('DOMContentLoaded', function() {
    // If there's a search term in URL, apply it
    const urlParams = new URLSearchParams(window.location.search);
    const searchTerm = urlParams.get('search');
    if (searchTerm) {
        document.getElementById('searchInput').value = searchTerm;
        searchItems();
    }
});
</script>
{% endblock %}
