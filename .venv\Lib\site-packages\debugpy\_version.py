
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-12-12T16:28:51-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "34cc53b2ac31b9de89a5445bbcfb0daddf33146e",
 "version": "1.8.11"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
