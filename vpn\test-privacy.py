#!/usr/bin/env python3
"""
Privacy VPN Testing Script
Tests various privacy and anonymity features to ensure they're working correctly
"""

import sys
import time
import requests
import subprocess
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.privacy.traffic_obfuscation import TrafficObfuscator, ProtocolMimicry
from src.privacy.anonymity_enhancer import PrivacyEnhancer, AnonymityMetrics

class PrivacyTester:
    """Comprehensive privacy testing suite"""
    
    def __init__(self):
        self.results = {}
        self.obfuscator = TrafficObfuscator()
        self.enhancer = PrivacyEnhancer()
        self.metrics = AnonymityMetrics()
        
    def run_all_tests(self):
        """Run all privacy tests"""
        print("🛡️  Starting Privacy VPN Tests...")
        print("=" * 50)
        
        # Test 1: IP Address Check
        self.test_ip_address()
        
        # Test 2: DNS Leak Test
        self.test_dns_leaks()
        
        # Test 3: WebRTC Leak Test
        self.test_webrtc_leaks()
        
        # Test 4: Traffic Obfuscation
        self.test_traffic_obfuscation()
        
        # Test 5: Protocol Mimicry
        self.test_protocol_mimicry()
        
        # Test 6: Anonymity Metrics
        self.test_anonymity_metrics()
        
        # Test 7: Privacy Enhancement
        self.test_privacy_enhancement()
        
        # Test 8: VPN Server Status
        self.test_vpn_server_status()
        
        # Generate report
        self.generate_report()
    
    def test_ip_address(self):
        """Test if IP address is properly masked"""
        print("🌐 Testing IP Address Masking...")
        
        try:
            # Get current IP
            response = requests.get('https://httpbin.org/ip', timeout=10)
            current_ip = response.json().get('origin', 'Unknown')
            
            print(f"   Current IP: {current_ip}")
            
            # Check if it's a private IP (indicating VPN is working)
            if current_ip.startswith(('10.', '172.', '192.168.')):
                self.results['ip_test'] = {'status': 'PASS', 'ip': current_ip, 'message': 'IP properly masked'}
                print("   ✅ IP address is properly masked")
            else:
                self.results['ip_test'] = {'status': 'WARNING', 'ip': current_ip, 'message': 'Real IP may be exposed'}
                print("   ⚠️  Real IP may be exposed")
                
        except Exception as e:
            self.results['ip_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ IP test failed: {e}")
    
    def test_dns_leaks(self):
        """Test for DNS leaks"""
        print("🔍 Testing DNS Leaks...")
        
        try:
            # Test DNS resolution
            result = subprocess.run(['nslookup', 'google.com'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                output = result.stdout
                
                # Check if VPN DNS server is being used
                if '********' in output or '127.0.0.1' in output:
                    self.results['dns_test'] = {'status': 'PASS', 'message': 'Using VPN DNS servers'}
                    print("   ✅ DNS queries are going through VPN")
                else:
                    self.results['dns_test'] = {'status': 'FAIL', 'message': 'DNS leak detected'}
                    print("   ❌ DNS leak detected - queries may be going to ISP DNS")
            else:
                self.results['dns_test'] = {'status': 'ERROR', 'message': 'DNS test failed'}
                print("   ❌ DNS test failed")
                
        except Exception as e:
            self.results['dns_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ DNS test error: {e}")
    
    def test_webrtc_leaks(self):
        """Test for WebRTC leaks (simplified)"""
        print("📡 Testing WebRTC Leaks...")
        
        try:
            # This is a simplified test - real WebRTC testing requires browser automation
            # Check if WebRTC blocking rules are in place
            result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
            
            if 'webrtc' in result.stdout.lower() or 'stun' in result.stdout.lower():
                self.results['webrtc_test'] = {'status': 'PASS', 'message': 'WebRTC blocking rules detected'}
                print("   ✅ WebRTC blocking rules are in place")
            else:
                self.results['webrtc_test'] = {'status': 'WARNING', 'message': 'No WebRTC blocking detected'}
                print("   ⚠️  No WebRTC blocking rules detected - configure browser manually")
                
        except Exception as e:
            self.results['webrtc_test'] = {'status': 'INFO', 'message': 'WebRTC test requires manual browser check'}
            print("   ℹ️  WebRTC test requires manual browser verification")
    
    def test_traffic_obfuscation(self):
        """Test traffic obfuscation functionality"""
        print("🔒 Testing Traffic Obfuscation...")
        
        try:
            # Test data
            original_data = b"This is test VPN traffic data for obfuscation testing"
            
            # Obfuscate
            obfuscated = self.obfuscator.obfuscate_packet(original_data)
            
            # Deobfuscate
            recovered = self.obfuscator.deobfuscate_packet(obfuscated)
            
            if original_data == recovered:
                self.results['obfuscation_test'] = {
                    'status': 'PASS', 
                    'original_size': len(original_data),
                    'obfuscated_size': len(obfuscated),
                    'message': 'Traffic obfuscation working correctly'
                }
                print(f"   ✅ Traffic obfuscation working (size: {len(original_data)} → {len(obfuscated)})")
            else:
                self.results['obfuscation_test'] = {'status': 'FAIL', 'message': 'Obfuscation/deobfuscation failed'}
                print("   ❌ Traffic obfuscation failed")
                
        except Exception as e:
            self.results['obfuscation_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Obfuscation test error: {e}")
    
    def test_protocol_mimicry(self):
        """Test protocol mimicry functionality"""
        print("🎭 Testing Protocol Mimicry...")
        
        try:
            mimicry = ProtocolMimicry()
            test_data = b"VPN data to be disguised"
            
            # Test different protocol mimicry
            protocols = ['http', 'https', 'dns']
            results = {}
            
            for protocol in protocols:
                disguised = mimicry.mimic_protocol(test_data, protocol)
                results[protocol] = len(disguised)
                
            self.results['mimicry_test'] = {
                'status': 'PASS',
                'protocols_tested': protocols,
                'sizes': results,
                'message': 'Protocol mimicry working'
            }
            print(f"   ✅ Protocol mimicry working for {len(protocols)} protocols")
            
        except Exception as e:
            self.results['mimicry_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Protocol mimicry test error: {e}")
    
    def test_anonymity_metrics(self):
        """Test anonymity metrics calculation"""
        print("📊 Testing Anonymity Metrics...")
        
        try:
            score = self.metrics.calculate_anonymity_score()
            recommendations = self.metrics.get_privacy_recommendations()
            
            self.results['anonymity_test'] = {
                'status': 'PASS',
                'anonymity_score': score,
                'recommendations_count': len(recommendations),
                'message': f'Anonymity score: {score:.1f}/100'
            }
            
            if score >= 80:
                print(f"   ✅ Excellent anonymity score: {score:.1f}/100")
            elif score >= 60:
                print(f"   ⚠️  Good anonymity score: {score:.1f}/100")
            else:
                print(f"   ❌ Low anonymity score: {score:.1f}/100 - needs improvement")
                
        except Exception as e:
            self.results['anonymity_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Anonymity metrics error: {e}")
    
    def test_privacy_enhancement(self):
        """Test privacy enhancement features"""
        print("🛡️  Testing Privacy Enhancement...")
        
        try:
            test_data = b"Test data for privacy enhancement"
            enhanced = self.enhancer.enhance_privacy(test_data)
            status = self.enhancer.get_privacy_status()
            
            self.results['enhancement_test'] = {
                'status': 'PASS',
                'original_size': len(test_data),
                'enhanced_size': len(enhanced),
                'privacy_mode': status.get('privacy_mode'),
                'anonymity_score': status.get('anonymity_score'),
                'message': 'Privacy enhancement working'
            }
            
            print(f"   ✅ Privacy enhancement active (mode: {status.get('privacy_mode')})")
            print(f"   📈 Anonymity score: {status.get('anonymity_score', 0):.1f}/100")
            
        except Exception as e:
            self.results['enhancement_test'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Privacy enhancement error: {e}")
    
    def test_vpn_server_status(self):
        """Test VPN server status"""
        print("🖥️  Testing VPN Server Status...")
        
        try:
            # Try to check server status
            result = subprocess.run(['python', 'cli/server_cli.py', 'status'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.results['server_test'] = {'status': 'PASS', 'message': 'VPN server is running'}
                print("   ✅ VPN server is running")
            else:
                self.results['server_test'] = {'status': 'WARNING', 'message': 'VPN server may not be running'}
                print("   ⚠️  VPN server may not be running")
                
        except Exception as e:
            self.results['server_test'] = {'status': 'INFO', 'message': 'Server status check requires VPN server'}
            print("   ℹ️  Server status check requires running VPN server")
    
    def generate_report(self):
        """Generate comprehensive privacy test report"""
        print("\n" + "=" * 50)
        print("🛡️  PRIVACY VPN TEST REPORT")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.results.values() if r['status'] == 'FAIL')
        warnings = sum(1 for r in self.results.values() if r['status'] == 'WARNING')
        
        print(f"📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⚠️  Warnings: {warnings}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for test_name, result in self.results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'WARNING': '⚠️',
                'ERROR': '🔥',
                'INFO': 'ℹ️'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result.get('message', 'No message')}")
        
        # Privacy recommendations
        if hasattr(self, 'metrics'):
            recommendations = self.metrics.get_privacy_recommendations()
            if recommendations:
                print(f"\n💡 Privacy Recommendations:")
                for i, rec in enumerate(recommendations[:5], 1):
                    print(f"   {i}. {rec}")
        
        print(f"\n🎯 Overall Privacy Status:")
        if failed_tests == 0:
            print("   🛡️  EXCELLENT - Your privacy is well protected!")
        elif failed_tests <= 2:
            print("   ⚠️  GOOD - Minor privacy improvements recommended")
        else:
            print("   ❌ NEEDS IMPROVEMENT - Several privacy issues detected")
        
        # Save report to file
        report_file = f"privacy-test-report-{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        print("\n🔒 Remember: Privacy is an ongoing process. Regular testing is recommended!")

def main():
    """Main entry point"""
    print("🛡️  Privacy VPN Testing Suite")
    print("Testing your VPN's privacy and anonymity features...\n")
    
    tester = PrivacyTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
