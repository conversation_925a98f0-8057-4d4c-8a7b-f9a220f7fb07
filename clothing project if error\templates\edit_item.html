{% extends "base.html" %}

{% block title %}Edit Item - My Personal Closet{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-edit me-3"></i>Edit Item</h1>
    <p>Update your clothing item details</p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="editItemForm">
                    <!-- Photo Upload -->
                    <div class="mb-4">
                        <label for="photo" class="form-label">
                            <i class="fas fa-camera me-2"></i>Item Photo
                        </label>
                        <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                            <div id="photoPreview" class="photo-preview">
                                {% if item.photo_filename %}
                                <img src="{{ url_for('static', filename='uploads/' + item.photo_filename) }}" 
                                     alt="{{ item.name }}" class="preview-image">
                                <p class="text-muted mt-2">Click to change photo</p>
                                {% else %}
                                <i class="fas fa-camera fa-3x text-muted mb-2"></i>
                                <p class="text-muted">Click to upload photo</p>
                                <small class="text-muted">Supports: JPG, PNG, GIF, WebP (Max 16MB)</small>
                                {% endif %}
                            </div>
                        </div>
                        <input type="file" class="form-control d-none" id="photo" name="photo" 
                               accept="image/*" onchange="previewPhoto(this)">
                    </div>

                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="name" class="form-label">Item Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   value="{{ item.name }}" placeholder="e.g., Blue Denim Jacket">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="top" {{ 'selected' if item.category == 'top' else '' }}>Top</option>
                                <option value="bottom" {{ 'selected' if item.category == 'bottom' else '' }}>Bottom</option>
                                <option value="dress" {{ 'selected' if item.category == 'dress' else '' }}>Dress</option>
                                <option value="outerwear" {{ 'selected' if item.category == 'outerwear' else '' }}>Outerwear</option>
                                <option value="shoes" {{ 'selected' if item.category == 'shoes' else '' }}>Shoes</option>
                                <option value="accessory" {{ 'selected' if item.category == 'accessory' else '' }}>Accessory</option>
                            </select>
                        </div>
                    </div>

                    <!-- Colors and Brand -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="colors" class="form-label">Colors *</label>
                            <input type="text" class="form-control" id="colors" name="colors" required 
                                   value="{{ item.colors|join(', ') }}" placeholder="e.g., Blue, White">
                            <small class="form-text text-muted">Separate multiple colors with commas</small>
                        </div>
                        <div class="col-md-6">
                            <label for="brand" class="form-label">Brand</label>
                            <input type="text" class="form-control" id="brand" name="brand" 
                                   value="{{ item.brand }}" placeholder="e.g., Levi's">
                        </div>
                    </div>

                    <!-- Size and Material -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="size" class="form-label">Size</label>
                            <input type="text" class="form-control" id="size" name="size" 
                                   value="{{ item.size }}" placeholder="e.g., M, 32, 8.5">
                        </div>
                        <div class="col-md-6">
                            <label for="material" class="form-label">Material</label>
                            <input type="text" class="form-control" id="material" name="material" 
                                   value="{{ item.material }}" placeholder="e.g., Cotton, Denim, Leather">
                        </div>
                    </div>

                    <!-- Purchase Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date" 
                                   value="{{ item.purchase_date }}">
                        </div>
                        <div class="col-md-6">
                            <label for="cost" class="form-label">Cost</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="cost" name="cost" 
                                       value="{{ item.cost if item.cost > 0 else '' }}" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <!-- Style Tags -->
                    <div class="mb-3">
                        <label class="form-label">Style Tags</label>
                        <div class="style-tags">
                            {% for style in styles %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="style_{{ style.name }}" 
                                       name="styles" value="{{ style.name }}" 
                                       {{ 'checked' if style in item.style else '' }}>
                                <label class="form-check-label" for="style_{{ style.name }}">
                                    {{ style.name.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Occasions -->
                    <div class="mb-3">
                        <label class="form-label">Suitable Occasions</label>
                        <div class="occasion-tags">
                            {% for occasion in occasions %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="occasion_{{ occasion.name }}" 
                                       name="occasions" value="{{ occasion.name }}" 
                                       {{ 'checked' if occasion in item.occasions else '' }}>
                                <label class="form-check-label" for="occasion_{{ occasion.name }}">
                                    {{ occasion.name.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Weather -->
                    <div class="mb-3">
                        <label class="form-label">Weather Conditions</label>
                        <div class="weather-tags">
                            {% for weather in weather_options %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="weather_{{ weather.name }}" 
                                       name="weather" value="{{ weather.name }}" 
                                       {{ 'checked' if weather in item.weather else '' }}>
                                <label class="form-check-label" for="weather_{{ weather.name }}">
                                    {{ weather.name.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Ratings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="formality" class="form-label">Formality Level</label>
                            <div class="rating-container">
                                <input type="range" class="form-range" id="formality" name="formality" 
                                       min="1" max="10" value="{{ item.formality }}" oninput="updateRatingDisplay('formality', this.value)">
                                <div class="rating-labels">
                                    <span>Casual</span>
                                    <span id="formalityValue">{{ item.formality }}</span>
                                    <span>Formal</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="versatility" class="form-label">Versatility</label>
                            <div class="rating-container">
                                <input type="range" class="form-range" id="versatility" name="versatility" 
                                       min="1" max="10" value="{{ item.versatility }}" oninput="updateRatingDisplay('versatility', this.value)">
                                <div class="rating-labels">
                                    <span>Specific</span>
                                    <span id="versatilityValue">{{ item.versatility }}</span>
                                    <span>Versatile</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Condition -->
                    <div class="mb-3">
                        <label for="condition" class="form-label">Condition</label>
                        <select class="form-select" id="condition" name="condition">
                            <option value="excellent" {{ 'selected' if item.condition == 'excellent' else '' }}>Excellent</option>
                            <option value="good" {{ 'selected' if item.condition == 'good' else '' }}>Good</option>
                            <option value="fair" {{ 'selected' if item.condition == 'fair' else '' }}>Fair</option>
                            <option value="poor" {{ 'selected' if item.condition == 'poor' else '' }}>Poor</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Any additional notes about this item...">{{ item.notes }}</textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Item
                        </button>
                        <a href="{{ url_for('view_item', item_id=item.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('photoPreview');
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" class="preview-image">
                <p class="text-muted mt-2">Click to change photo</p>
            `;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function updateRatingDisplay(type, value) {
    document.getElementById(type + 'Value').textContent = value;
}
</script>
{% endblock %}
